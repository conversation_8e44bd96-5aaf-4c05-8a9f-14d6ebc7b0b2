openapi: 3.0.3
info:
  title: Jewelry E-Commerce Platform API
  description: |
    A comprehensive REST API for a curated jewelry showcase platform where admins upload items, 
    create collections with shareable links, customers select items and place orders, 
    and admins confirm orders via phone calls.
    
    ## Features
    - Product management with image upload
    - Collection management with shareable URLs
    - Order processing and customer management
    - Inventory tracking with alerts
    - Image storage with multiple size variants
    
    ## Authentication
    Currently, most endpoints are public for development. Authentication will be added in Phase 2.
    
    ## Image Storage
    Images are stored using MinIO (local development) or AWS S3 (production) with automatic
    generation of multiple size variants (thumbnail, small, medium, large).
    
  version: 1.0.0
  contact:
    name: Anand Jewels API Support
    email: <EMAIL>
  license:
    name: MIT
    url: https://opensource.org/licenses/MIT

servers:
  - url: http://localhost:8080/api/v1
    description: Local development server
  - url: https://api.anandjewels.com/v1
    description: Production server

tags:
  - name: Health
    description: Health check and system status
  - name: Products
    description: Product management operations
  - name: Collections
    description: Collection management and sharing
  - name: Orders
    description: Order processing and management
  - name: Customers
    description: Customer management
  - name: Inventory
    description: Inventory tracking and alerts
  - name: Upload
    description: Image upload and management
  - name: Image Serving
    description: Optimized image serving and URL generation
  - name: Image Proxy
    description: Direct image serving with optimization

paths:
  /health:
    get:
      tags:
        - Health
      summary: Health check
      description: Check the health status of the API and its dependencies
      responses:
        '200':
          description: Service is healthy
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                    example: healthy
                  service:
                    type: string
                    example: jewelry-backend
                  version:
                    type: string
                    example: 1.0.0
                  database:
                    type: string
                    example: connected
                  redis:
                    type: string
                    example: connected

  /db-info:
    get:
      tags:
        - Health
      summary: Database information
      description: Get detailed database and Redis connection information
      responses:
        '200':
          description: Database information retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  postgres_info:
                    type: object
                  redis_info:
                    type: object

  /products:
    get:
      tags:
        - Products
      summary: List products
      description: Retrieve a paginated list of products with optional filtering and search
      parameters:
        - name: page
          in: query
          description: Page number (1-based)
          schema:
            type: integer
            minimum: 1
            default: 1
        - name: limit
          in: query
          description: Number of items per page
          schema:
            type: integer
            minimum: 1
            maximum: 100
            default: 20
        - name: category
          in: query
          description: Filter by product category
          schema:
            type: string
            enum: [rings, necklaces, earrings, bracelets, bangles, anklets, nose_pins, pendants]
        - name: search
          in: query
          description: Search in product name and description
          schema:
            type: string
      responses:
        '200':
          description: Products retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  products:
                    type: array
                    items:
                      $ref: '#/components/schemas/Product'
                  total:
                    type: integer
                  page:
                    type: integer
                  limit:
                    type: integer

    post:
      tags:
        - Products
      summary: Create product
      description: Create a new product
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateProductRequest'
      responses:
        '201':
          description: Product created successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Product'

  /products/{id}:
    get:
      tags:
        - Products
      summary: Get product
      description: Retrieve a single product by ID with images
      parameters:
        - name: id
          in: path
          required: true
          description: Product ID
          schema:
            type: string
            format: uuid
      responses:
        '200':
          description: Product retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Product'

    put:
      tags:
        - Products
      summary: Update product
      description: Update an existing product by ID
      parameters:
        - name: id
          in: path
          required: true
          description: Product ID
          schema:
            type: string
            format: uuid
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateProductRequest'
      responses:
        '200':
          description: Product updated successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Product'

    delete:
      tags:
        - Products
      summary: Delete product
      description: Delete a product by ID (soft delete)
      parameters:
        - name: id
          in: path
          required: true
          description: Product ID
          schema:
            type: string
            format: uuid
      responses:
        '200':
          description: Product deleted successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                  product_id:
                    type: string
                    format: uuid

  /products/{id}/images:
    post:
      tags:
        - Upload
      summary: Upload product image
      description: Upload an image file for a specific product with automatic variant generation
      parameters:
        - name: id
          in: path
          required: true
          description: Product ID
          schema:
            type: string
            format: uuid
      requestBody:
        required: true
        content:
          multipart/form-data:
            schema:
              type: object
              properties:
                image:
                  type: string
                  format: binary
                  description: Image file (JPEG, PNG, WebP, max 10MB)
                is_primary:
                  type: boolean
                  description: Set as primary image
                  default: false
                alt_text:
                  type: string
                  description: Alt text for accessibility
              required:
                - image
      responses:
        '200':
          description: Image uploaded successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                  image_id:
                    type: string
                    format: uuid
                  storage_id:
                    type: string
                  image:
                    $ref: '#/components/schemas/UploadedImage'

  /images/{id}/{image_id}/optimized:
    get:
      tags:
        - Image Serving
      summary: Get optimized image URL
      description: Generate optimized image URLs with multiple format and size variants
      parameters:
        - name: id
          in: path
          required: true
          description: Product ID
          schema:
            type: string
            format: uuid
        - name: image_id
          in: path
          required: true
          description: Image ID
          schema:
            type: string
            format: uuid
        - name: size
          in: query
          description: Image size variant
          schema:
            type: string
            enum: [thumbnail, small, medium, large, original]
            default: medium
        - name: format
          in: query
          description: Preferred image format
          schema:
            type: string
            enum: [webp, jpeg, png, avif]
        - name: quality
          in: query
          description: Image quality (1-100)
          schema:
            type: integer
            minimum: 1
            maximum: 100
            default: 85
        - name: device
          in: query
          description: Target device type
          schema:
            type: string
            enum: [desktop, mobile, tablet]
            default: desktop
        - name: webp
          in: query
          description: Enable WebP variant
          schema:
            type: boolean
            default: true
        - name: avif
          in: query
          description: Enable AVIF variant
          schema:
            type: boolean
            default: false
        - name: responsive
          in: query
          description: Generate responsive image set
          schema:
            type: boolean
            default: false
      responses:
        '200':
          description: Optimized image URLs generated successfully
          content:
            application/json:
              schema:
                oneOf:
                  - $ref: '#/components/schemas/OptimizedImageURL'
                  - $ref: '#/components/schemas/ResponsiveImageSet'

  /images/{id}/{image_id}/serve:
    get:
      tags:
        - Image Serving
      summary: Serve optimized image
      description: Serve an optimized image file directly with proper headers
      parameters:
        - name: id
          in: path
          required: true
          description: Product ID
          schema:
            type: string
            format: uuid
        - name: image_id
          in: path
          required: true
          description: Image ID
          schema:
            type: string
            format: uuid
        - name: size
          in: query
          description: Image size variant
          schema:
            type: string
            enum: [thumbnail, small, medium, large, original]
            default: medium
        - name: format
          in: query
          description: Preferred image format
          schema:
            type: string
            enum: [webp, jpeg, png, avif]
      responses:
        '200':
          description: Optimized image file
          content:
            image/*:
              schema:
                type: string
                format: binary
        '302':
          description: Redirect to optimized image URL

  /images/{id}/set:
    get:
      tags:
        - Image Serving
      summary: Get product image set
      description: Generate optimized image URLs for all product images with responsive variants
      parameters:
        - name: id
          in: path
          required: true
          description: Product ID
          schema:
            type: string
            format: uuid
        - name: size
          in: query
          description: Default image size
          schema:
            type: string
            enum: [thumbnail, small, medium, large, original]
            default: medium
        - name: device
          in: query
          description: Target device type
          schema:
            type: string
            enum: [desktop, mobile, tablet]
            default: desktop
        - name: webp
          in: query
          description: Enable WebP variants
          schema:
            type: boolean
            default: true
        - name: avif
          in: query
          description: Enable AVIF variants
          schema:
            type: boolean
            default: false
      responses:
        '200':
          description: Product image set retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ProductImageSet'

  /images/placeholder:
    get:
      tags:
        - Image Serving
      summary: Get image placeholder
      description: Generate SVG placeholder image for lazy loading
      parameters:
        - name: width
          in: query
          description: Placeholder width
          schema:
            type: integer
            minimum: 1
            maximum: 2000
            default: 300
        - name: height
          in: query
          description: Placeholder height
          schema:
            type: integer
            minimum: 1
            maximum: 2000
            default: 300
        - name: color
          in: query
          description: Placeholder background color (hex)
          schema:
            type: string
            pattern: '^[0-9a-fA-F]{6}$'
            default: 'f3f4f6'
      responses:
        '200':
          description: Placeholder generated successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ImagePlaceholder'

  /proxy/health:
    get:
      tags:
        - Image Proxy
      summary: Image proxy health check
      description: Check if image proxy and optimization services are working
      responses:
        '200':
          description: Image proxy is healthy
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ImageProxyHealth'

  /proxy/images/{id}/{image_id}:
    get:
      tags:
        - Image Proxy
      summary: Proxy product image
      description: Serve a product image through the proxy with optimization and caching headers
      parameters:
        - name: id
          in: path
          required: true
          description: Product ID
          schema:
            type: string
            format: uuid
        - name: image_id
          in: path
          required: true
          description: Image ID
          schema:
            type: string
            format: uuid
        - name: size
          in: query
          description: Image size variant
          schema:
            type: string
            enum: [thumbnail, small, medium, large, original]
            default: medium
        - name: quality
          in: query
          description: Image quality (1-100)
          schema:
            type: integer
            minimum: 1
            maximum: 100
      responses:
        '200':
          description: Image served successfully
          content:
            image/*:
              schema:
                type: string
                format: binary

  /proxy/images/{id}/{image_id}/info:
    get:
      tags:
        - Image Proxy
      summary: Get image information
      description: Get metadata and optimization information for a product image
      parameters:
        - name: id
          in: path
          required: true
          description: Product ID
          schema:
            type: string
            format: uuid
        - name: image_id
          in: path
          required: true
          description: Image ID
          schema:
            type: string
            format: uuid
      responses:
        '200':
          description: Image information retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ImageInfo'

  /products/{id}/images/{image_id}:
    get:
      tags:
        - Upload
      summary: Get image information
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
            format: uuid
        - name: image_id
          in: path
          required: true
          schema:
            type: string
            format: uuid
      responses:
        '200':
          description: Image information retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  id:
                    type: string
                    format: uuid
                  image_url:
                    type: string
                    format: uri
                  is_primary:
                    type: boolean

    delete:
      tags:
        - Upload
      summary: Delete product image
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
            format: uuid
        - name: image_id
          in: path
          required: true
          schema:
            type: string
            format: uuid
      responses:
        '200':
          description: Image deleted successfully

  /collections:
    get:
      tags:
        - Collections
      summary: List collections
      description: Retrieve a paginated list of collections with optional filtering
      parameters:
        - name: page
          in: query
          schema:
            type: integer
            minimum: 1
            default: 1
        - name: limit
          in: query
          schema:
            type: integer
            minimum: 1
            maximum: 100
            default: 20
        - name: is_public
          in: query
          schema:
            type: boolean
        - name: search
          in: query
          schema:
            type: string
      responses:
        '200':
          description: Collections retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  collections:
                    type: array
                    items:
                      $ref: '#/components/schemas/Collection'
                  total:
                    type: integer

    post:
      tags:
        - Collections
      summary: Create collection
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateCollectionRequest'
      responses:
        '201':
          description: Collection created successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Collection'

  /collections/{id}:
    get:
      tags:
        - Collections
      summary: Get collection
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
            format: uuid
      responses:
        '200':
          description: Collection retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Collection'

    put:
      tags:
        - Collections
      summary: Update collection
      description: Update an existing collection by ID
      parameters:
        - name: id
          in: path
          required: true
          description: Collection ID
          schema:
            type: string
            format: uuid
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateCollectionRequest'
      responses:
        '200':
          description: Collection updated successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Collection'

    delete:
      tags:
        - Collections
      summary: Delete collection
      description: Delete a collection by ID (soft delete)
      parameters:
        - name: id
          in: path
          required: true
          description: Collection ID
          schema:
            type: string
            format: uuid
      responses:
        '200':
          description: Collection deleted successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                  collection_id:
                    type: string
                    format: uuid

  /collections/slug/{slug}:
    get:
      tags:
        - Collections
      summary: Get collection by slug
      description: Retrieve a public collection by its slug (for shareable URLs)
      parameters:
        - name: slug
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Collection retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Collection'

  /collections/{id}/products:
    get:
      tags:
        - Collections
      summary: Get collection with products
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
            format: uuid
      responses:
        '200':
          description: Collection with products retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CollectionWithProducts'

    post:
      tags:
        - Collections
      summary: Add product to collection
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
            format: uuid
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - product_id
              properties:
                product_id:
                  type: string
                  format: uuid
                display_order:
                  type: integer
                is_featured:
                  type: boolean
      responses:
        '200':
          description: Product added to collection successfully

  /collections/{id}/products/{product_id}:
    delete:
      tags:
        - Collections
      summary: Remove product from collection
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
            format: uuid
        - name: product_id
          in: path
          required: true
          schema:
            type: string
            format: uuid
      responses:
        '200':
          description: Product removed from collection successfully

  /orders:
    get:
      tags:
        - Orders
      summary: List orders
      description: Retrieve a paginated list of orders with optional filtering
      parameters:
        - name: page
          in: query
          schema:
            type: integer
            minimum: 1
            default: 1
        - name: limit
          in: query
          schema:
            type: integer
            minimum: 1
            maximum: 100
            default: 20
        - name: customer_id
          in: query
          schema:
            type: string
            format: uuid
        - name: status
          in: query
          schema:
            type: string
            enum: [pending, confirmed, processing, shipped, delivered, cancelled]
        - name: search
          in: query
          schema:
            type: string
      responses:
        '200':
          description: Orders retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  orders:
                    type: array
                    items:
                      $ref: '#/components/schemas/Order'
                  total:
                    type: integer

    post:
      tags:
        - Orders
      summary: Create order
      description: Create a new order with automatic inventory management
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateOrderRequest'
      responses:
        '201':
          description: Order created successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Order'

  /orders/{id}:
    get:
      tags:
        - Orders
      summary: Get order
      description: Retrieve a single order by ID with items and customer details
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
            format: uuid
      responses:
        '200':
          description: Order retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Order'

  /orders/{id}/status:
    put:
      tags:
        - Orders
      summary: Update order status
      description: Update the status of an existing order
      parameters:
        - name: id
          in: path
          required: true
          description: Order ID
          schema:
            type: string
            format: uuid
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - status
              properties:
                status:
                  type: string
                  enum: [pending, confirmed, processing, shipped, delivered, cancelled]
                  description: New order status
      responses:
        '200':
          description: Order status updated successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Order'

  /customers:
    get:
      tags:
        - Customers
      summary: List customers
      description: Retrieve a paginated list of customers with optional filtering
      parameters:
        - name: page
          in: query
          schema:
            type: integer
            minimum: 1
            default: 1
        - name: limit
          in: query
          schema:
            type: integer
            minimum: 1
            maximum: 100
            default: 20
        - name: is_active
          in: query
          schema:
            type: boolean
        - name: search
          in: query
          schema:
            type: string
      responses:
        '200':
          description: Customers retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  customers:
                    type: array
                    items:
                      $ref: '#/components/schemas/Customer'
                  total:
                    type: integer

    post:
      tags:
        - Customers
      summary: Create customer
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateCustomerRequest'
      responses:
        '201':
          description: Customer created successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Customer'

  /customers/{id}:
    get:
      tags:
        - Customers
      summary: Get customer
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
            format: uuid
      responses:
        '200':
          description: Customer retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Customer'

    put:
      tags:
        - Customers
      summary: Update customer
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
            format: uuid
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateCustomerRequest'
      responses:
        '200':
          description: Customer updated successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Customer'

  /inventory:
    get:
      tags:
        - Inventory
      summary: Get inventory status
      description: Retrieve inventory status for all products with optional filtering
      parameters:
        - name: page
          in: query
          schema:
            type: integer
            minimum: 1
            default: 1
        - name: limit
          in: query
          schema:
            type: integer
            minimum: 1
            maximum: 100
            default: 20
        - name: category
          in: query
          schema:
            type: string
        - name: low_stock_only
          in: query
          schema:
            type: boolean
        - name: search
          in: query
          schema:
            type: string
      responses:
        '200':
          description: Inventory status retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  inventory:
                    type: array
                    items:
                      $ref: '#/components/schemas/InventoryStatus'
                  total:
                    type: integer

  /inventory/{id}:
    put:
      tags:
        - Inventory
      summary: Update product inventory
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
            format: uuid
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateInventoryRequest'
      responses:
        '200':
          description: Inventory updated successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/InventoryLog'

  /inventory/logs:
    get:
      tags:
        - Inventory
      summary: Get inventory logs
      parameters:
        - name: page
          in: query
          schema:
            type: integer
            minimum: 1
            default: 1
        - name: limit
          in: query
          schema:
            type: integer
            minimum: 1
            maximum: 100
            default: 20
        - name: product_id
          in: query
          schema:
            type: string
            format: uuid
      responses:
        '200':
          description: Inventory logs retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  logs:
                    type: array
                    items:
                      $ref: '#/components/schemas/InventoryLog'
                  total:
                    type: integer

  /inventory/alerts/low-stock:
    get:
      tags:
        - Inventory
      summary: Get low stock alerts
      description: Get products that are at or below minimum stock level
      responses:
        '200':
          description: Low stock alerts retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  alerts:
                    type: array
                    items:
                      $ref: '#/components/schemas/InventoryStatus'
                  total:
                    type: integer

  /inventory/alerts/out-of-stock:
    get:
      tags:
        - Inventory
      summary: Get out of stock alerts
      description: Get products that are completely out of stock
      responses:
        '200':
          description: Out of stock alerts retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  alerts:
                    type: array
                    items:
                      $ref: '#/components/schemas/InventoryStatus'
                  total:
                    type: integer

components:
  schemas:
    Product:
      type: object
      properties:
        id:
          type: string
          format: uuid
        sku:
          type: string
        name:
          type: string
        description:
          type: string
        price:
          type: number
          format: float
        category:
          type: string
          enum: [rings, necklaces, earrings, bracelets, bangles, anklets, nose_pins, pendants]
        stock_quantity:
          type: integer
        is_featured:
          type: boolean
        is_active:
          type: boolean
        created_at:
          type: string
          format: date-time
        updated_at:
          type: string
          format: date-time

    CreateProductRequest:
      type: object
      required:
        - sku
        - name
        - price
        - category
        - stock_quantity
      properties:
        sku:
          type: string
        name:
          type: string
        description:
          type: string
        price:
          type: number
          format: float
          minimum: 0
        category:
          type: string
          enum: [rings, necklaces, earrings, bracelets, bangles, anklets, nose_pins, pendants]
        stock_quantity:
          type: integer
          minimum: 0
        is_featured:
          type: boolean
          default: false

    UploadedImage:
      type: object
      properties:
        id:
          type: string
        original:
          $ref: '#/components/schemas/ImageVariant'
        variants:
          type: object
          additionalProperties:
            $ref: '#/components/schemas/ImageVariant'
        created_at:
          type: string
          format: date-time

    ImageVariant:
      type: object
      properties:
        url:
          type: string
          format: uri
        width:
          type: integer
        height:
          type: integer
        size:
          type: integer
          format: int64
        filename:
          type: string

    OptimizedImageURL:
      type: object
      properties:
        url:
          type: string
          format: uri
          description: Optimized image URL
        webp_url:
          type: string
          format: uri
          description: WebP variant URL
        avif_url:
          type: string
          format: uri
          description: AVIF variant URL
        width:
          type: integer
          description: Image width in pixels
        height:
          type: integer
          description: Image height in pixels
        format:
          type: string
          enum: [webp, jpeg, png, avif]
          description: Image format
        size:
          type: string
          enum: [thumbnail, small, medium, large, original]
          description: Image size variant
        cache_max_age:
          type: integer
          description: Cache duration in seconds
        headers:
          type: object
          additionalProperties:
            type: string
          description: HTTP headers for optimization

    ResponsiveImageSet:
      type: object
      properties:
        default:
          $ref: '#/components/schemas/OptimizedImageURL'
          description: Default image for fallback
        variants:
          type: array
          items:
            $ref: '#/components/schemas/OptimizedImageURL'
          description: All size variants
        srcset:
          type: string
          description: HTML srcset attribute value
        sizes:
          type: string
          description: HTML sizes attribute value
        lazy_load:
          type: boolean
          description: Whether lazy loading is enabled

    ProductImageSet:
      type: object
      properties:
        product_id:
          type: string
          format: uuid
        primary_image:
          type: object
          properties:
            id:
              type: string
              format: uuid
            alt_text:
              type: string
            display_order:
              type: integer
            is_primary:
              type: boolean
            image_set:
              $ref: '#/components/schemas/ResponsiveImageSet'
        images:
          type: array
          items:
            type: object
            properties:
              id:
                type: string
                format: uuid
              alt_text:
                type: string
              display_order:
                type: integer
              is_primary:
                type: boolean
              image_set:
                $ref: '#/components/schemas/ResponsiveImageSet'
        total_images:
          type: integer
        options:
          type: object
          description: Applied optimization options

    ImagePlaceholder:
      type: object
      properties:
        placeholder_url:
          type: string
          description: Base64 encoded SVG placeholder
        width:
          type: integer
          description: Placeholder width
        height:
          type: integer
          description: Placeholder height
        type:
          type: string
          enum: [svg_placeholder]
          description: Placeholder type

    ImageProxyHealth:
      type: object
      properties:
        status:
          type: string
          enum: [healthy, unhealthy]
        storage_healthy:
          type: boolean
          description: Whether storage is accessible
        storage_url:
          type: string
          format: uri
          description: Storage base URL
        supported_formats:
          type: array
          items:
            type: string
          description: Supported image formats
        avif_supported:
          type: boolean
          description: Whether AVIF format is supported
        optimization_enabled:
          type: boolean
          description: Whether optimization is enabled
        caching_enabled:
          type: boolean
          description: Whether caching is enabled
        timestamp:
          type: string
          format: date-time
          description: Health check timestamp

    ImageInfo:
      type: object
      properties:
        id:
          type: string
          format: uuid
        product_id:
          type: string
          format: uuid
        alt_text:
          type: string
        display_order:
          type: integer
        is_primary:
          type: boolean
        created_at:
          type: string
          format: date-time
        storage_path:
          type: string
        original_url:
          type: string
          format: uri
        variants:
          type: object
          additionalProperties:
            type: object
            properties:
              url:
                type: string
                format: uri
              width:
                type: integer
              height:
                type: integer
        optimal_format:
          type: string
          enum: [webp, jpeg, png, avif]
        proxy_url:
          type: string
          description: Proxy URL for this image

    Collection:
      type: object
      properties:
        id:
          type: string
          format: uuid
        slug:
          type: string
        name:
          type: string
        description:
          type: string
        cover_image_url:
          type: string
          format: uri
        is_public:
          type: boolean
        is_active:
          type: boolean
        view_count:
          type: integer
        product_count:
          type: integer
        created_at:
          type: string
          format: date-time
        updated_at:
          type: string
          format: date-time

    CreateCollectionRequest:
      type: object
      required:
        - slug
        - name
      properties:
        slug:
          type: string
          pattern: '^[a-z0-9-]+$'
        name:
          type: string
        description:
          type: string
        cover_image_url:
          type: string
          format: uri
        is_public:
          type: boolean
          default: true

    UpdateCollectionRequest:
      type: object
      properties:
        slug:
          type: string
          pattern: '^[a-z0-9-]+$'
        name:
          type: string
        description:
          type: string
        cover_image_url:
          type: string
          format: uri
        is_public:
          type: boolean
        expires_at:
          type: string
          format: date-time

    CollectionWithProducts:
      allOf:
        - $ref: '#/components/schemas/Collection'
        - type: object
          properties:
            products:
              type: array
              items:
                $ref: '#/components/schemas/Product'

    Customer:
      type: object
      properties:
        id:
          type: string
          format: uuid
        name:
          type: string
        email:
          type: string
          format: email
        phone:
          type: string
        address:
          type: object
          properties:
            street:
              type: string
            city:
              type: string
            state:
              type: string
            country:
              type: string
            pincode:
              type: string
        total_orders:
          type: integer
        total_spent:
          type: number
          format: float
        last_order_date:
          type: string
          format: date-time
        is_active:
          type: boolean
        created_at:
          type: string
          format: date-time
        updated_at:
          type: string
          format: date-time

    CreateCustomerRequest:
      type: object
      required:
        - name
        - phone
      properties:
        name:
          type: string
        email:
          type: string
          format: email
        phone:
          type: string
        address:
          type: object
          properties:
            street:
              type: string
            city:
              type: string
            state:
              type: string
            country:
              type: string
            pincode:
              type: string

    Order:
      type: object
      properties:
        id:
          type: string
          format: uuid
        order_number:
          type: string
        customer_id:
          type: string
          format: uuid
        status:
          type: string
          enum: [pending, confirmed, processing, shipped, delivered, cancelled]
        total_amount:
          type: number
          format: float
        discount_amount:
          type: number
          format: float
        final_amount:
          type: number
          format: float
        delivery_address:
          type: object
          properties:
            street:
              type: string
            city:
              type: string
            state:
              type: string
            country:
              type: string
            pincode:
              type: string
        notes:
          type: string
        created_at:
          type: string
          format: date-time
        updated_at:
          type: string
          format: date-time
        customer:
          $ref: '#/components/schemas/Customer'
        items:
          type: array
          items:
            $ref: '#/components/schemas/OrderItem'

    OrderItem:
      type: object
      properties:
        id:
          type: string
          format: uuid
        product_id:
          type: string
          format: uuid
        quantity:
          type: integer
        unit_price:
          type: number
          format: float
        total_price:
          type: number
          format: float
        product:
          $ref: '#/components/schemas/Product'

    CreateOrderRequest:
      type: object
      required:
        - customer_id
        - items
        - delivery_address
      properties:
        customer_id:
          type: string
          format: uuid
        items:
          type: array
          minItems: 1
          items:
            type: object
            required:
              - product_id
              - quantity
            properties:
              product_id:
                type: string
                format: uuid
              quantity:
                type: integer
                minimum: 1
        delivery_address:
          type: object
          required:
            - street
            - city
            - state
            - country
          properties:
            street:
              type: string
            city:
              type: string
            state:
              type: string
            country:
              type: string
            pincode:
              type: string
        discount_amount:
          type: number
          format: float
          minimum: 0
          default: 0
        notes:
          type: string

    InventoryStatus:
      type: object
      properties:
        product_id:
          type: string
          format: uuid
        product_name:
          type: string
        product_sku:
          type: string
        current_stock:
          type: integer
        min_stock_level:
          type: integer
        is_low_stock:
          type: boolean
        is_out_of_stock:
          type: boolean
        last_updated:
          type: string
          format: date-time

    InventoryLog:
      type: object
      properties:
        id:
          type: string
          format: uuid
        product_id:
          type: string
          format: uuid
        change_type:
          type: string
          enum: [increase, decrease, adjust]
        quantity_change:
          type: integer
        previous_stock:
          type: integer
        new_stock:
          type: integer
        reason:
          type: string
        created_at:
          type: string
          format: date-time

    UpdateInventoryRequest:
      type: object
      required:
        - quantity
        - change_type
        - reason
      properties:
        quantity:
          type: integer
          minimum: 0
        change_type:
          type: string
          enum: [increase, decrease, adjust]
        reason:
          type: string
          minLength: 1
          maxLength: 255

    ErrorResponse:
      type: object
      properties:
        error:
          type: string
        message:
          type: string
        code:
          type: integer
        details:
          type: string

  responses:
    BadRequest:
      description: Bad request
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'
    NotFound:
      description: Resource not found
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'
    InternalServerError:
      description: Internal server error
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'

security: []
