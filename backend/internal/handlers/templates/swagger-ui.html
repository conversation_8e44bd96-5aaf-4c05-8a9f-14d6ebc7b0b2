<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{.Title}} - API Documentation</title>
    <link rel="stylesheet" type="text/css" href="https://unpkg.com/swagger-ui-dist@5.10.5/swagger-ui.css" />
    <style>
        html {
            box-sizing: border-box;
            overflow: -moz-scrollbars-vertical;
            overflow-y: scroll;
        }
        *, *:before, *:after {
            box-sizing: inherit;
        }
        body {
            margin: 0;
            background: #fafafa;
        }
        .swagger-ui .topbar {
            background-color: #1f2937;
        }
        .swagger-ui .topbar .download-url-wrapper .select-label {
            color: #ffffff;
        }
        .swagger-ui .topbar .download-url-wrapper input[type=text] {
            border: 2px solid #374151;
        }
        .custom-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            text-align: center;
            margin-bottom: 0;
        }
        .custom-header h1 {
            margin: 0;
            font-size: 2.5em;
            font-weight: 300;
        }
        .custom-header p {
            margin: 10px 0 0 0;
            font-size: 1.2em;
            opacity: 0.9;
        }
        .api-info {
            background: white;
            padding: 20px;
            margin: 0;
            border-bottom: 1px solid #e5e7eb;
        }
        .api-info h2 {
            margin-top: 0;
            color: #1f2937;
        }
        .api-stats {
            display: flex;
            gap: 20px;
            flex-wrap: wrap;
            margin-top: 15px;
        }
        .stat-item {
            background: #f3f4f6;
            padding: 10px 15px;
            border-radius: 8px;
            text-align: center;
            min-width: 120px;
        }
        .stat-number {
            font-size: 1.5em;
            font-weight: bold;
            color: #667eea;
        }
        .stat-label {
            font-size: 0.9em;
            color: #6b7280;
            margin-top: 5px;
        }
        .quick-links {
            background: #f9fafb;
            padding: 15px 20px;
            border-bottom: 1px solid #e5e7eb;
        }
        .quick-links h3 {
            margin: 0 0 10px 0;
            color: #1f2937;
        }
        .links-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
        }
        .link-item {
            background: white;
            padding: 10px;
            border-radius: 6px;
            border: 1px solid #e5e7eb;
            text-decoration: none;
            color: #374151;
            transition: all 0.2s;
            cursor: pointer;
        }
        .link-item:hover {
            border-color: #667eea;
            background: #f8faff;
        }
        .link-title {
            font-weight: 600;
            color: #667eea;
        }
        .link-desc {
            font-size: 0.9em;
            color: #6b7280;
            margin-top: 2px;
        }
    </style>
</head>
<body>
    <div class="custom-header">
        <h1>🏺 {{.Title}}</h1>
        <p>{{.Description}} - Version {{.Version}}</p>
    </div>

    <div class="api-info">
        <h2>API Overview</h2>
        <p>A complete REST API for managing jewelry products, collections, orders, customers, and inventory. Built with Go, PostgreSQL, Redis, and MinIO for a modern e-commerce experience.</p>
        
        <div class="api-stats">
            <div class="stat-item">
                <div class="stat-number">28+</div>
                <div class="stat-label">API Endpoints</div>
            </div>
            <div class="stat-item">
                <div class="stat-number">6</div>
                <div class="stat-label">Core Modules</div>
            </div>
            <div class="stat-item">
                <div class="stat-number">11</div>
                <div class="stat-label">Database Tables</div>
            </div>
            <div class="stat-item">
                <div class="stat-number">100%</div>
                <div class="stat-label">OpenAPI 3.0</div>
            </div>
        </div>
    </div>

    <div class="quick-links">
        <h3>🚀 Quick Access</h3>
        <div class="links-grid">
            <div class="link-item" onclick="scrollToTag('Products')">
                <div class="link-title">📦 Products</div>
                <div class="link-desc">Manage jewelry items with images</div>
            </div>
            <div class="link-item" onclick="scrollToTag('Collections')">
                <div class="link-title">📚 Collections</div>
                <div class="link-desc">Curated product groups & sharing</div>
            </div>
            <div class="link-item" onclick="scrollToTag('Orders')">
                <div class="link-title">🛒 Orders</div>
                <div class="link-desc">Order processing & management</div>
            </div>
            <div class="link-item" onclick="scrollToTag('Customers')">
                <div class="link-title">👥 Customers</div>
                <div class="link-desc">Customer information & statistics</div>
            </div>
            <div class="link-item" onclick="scrollToTag('Inventory')">
                <div class="link-title">📊 Inventory</div>
                <div class="link-desc">Stock tracking & alerts</div>
            </div>
            <div class="link-item" onclick="scrollToTag('Upload')">
                <div class="link-title">🖼️ Upload</div>
                <div class="link-desc">Image management & variants</div>
            </div>
        </div>
    </div>

    <div id="swagger-ui"></div>

    <script src="https://unpkg.com/swagger-ui-dist@5.10.5/swagger-ui-bundle.js"></script>
    <script src="https://unpkg.com/swagger-ui-dist@5.10.5/swagger-ui-standalone-preset.js"></script>
    <script>
        window.onload = function() {
            const ui = SwaggerUIBundle({
                url: '/api/docs/spec.yaml',
                dom_id: '#swagger-ui',
                deepLinking: true,
                presets: [
                    SwaggerUIBundle.presets.apis,
                    SwaggerUIStandalonePreset
                ],
                plugins: [
                    SwaggerUIBundle.plugins.DownloadUrl
                ],
                layout: "StandaloneLayout",
                defaultModelsExpandDepth: 1,
                defaultModelExpandDepth: 1,
                docExpansion: "list",
                filter: true,
                showExtensions: true,
                showCommonExtensions: true,
                tryItOutEnabled: true,
                validatorUrl: null
            });

            window.ui = ui;
        };

        function scrollToTag(tagName) {
            setTimeout(() => {
                const element = document.querySelector(`[data-tag="${tagName}"]`);
                if (element) {
                    element.scrollIntoView({ behavior: 'smooth' });
                } else {
                    // Fallback: try to find by text content
                    const elements = document.querySelectorAll('.opblock-tag-section h3');
                    for (let el of elements) {
                        if (el.textContent.includes(tagName)) {
                            el.scrollIntoView({ behavior: 'smooth' });
                            break;
                        }
                    }
                }
            }, 1000); // Wait for Swagger UI to load
        }
    </script>
</body>
</html>
