package services

import (
	"encoding/base64"
	"fmt"
	"os"
	"strings"
	"time"
)

// ImageServingService handles optimized image delivery from local storage
type ImageServingService struct {
	storageBaseURL string
	cacheMaxAge    int
}

// ImageFormat represents different image formats for optimization
type ImageFormat string

const (
	FormatWebP ImageFormat = "webp"
	FormatJPEG ImageFormat = "jpeg"
	FormatPNG  ImageFormat = "png"
	FormatAVIF ImageFormat = "avif"
)

// DeviceType represents different device categories for optimization
type DeviceType string

const (
	DeviceDesktop DeviceType = "desktop"
	DeviceMobile  DeviceType = "mobile"
	DeviceTablet  DeviceType = "tablet"
)

// ImageSize represents predefined image sizes
type ImageSize string

const (
	SizeThumbnail ImageSize = "thumbnail" // 150x150
	SizeSmall     ImageSize = "small"     // 300x300
	SizeMedium    ImageSize = "medium"    // 600x600
	SizeLarge     ImageSize = "large"     // 1200x1200
	SizeOriginal  ImageSize = "original"  // Full size
)

// ImageServingOptions contains options for image URL generation
type ImageServingOptions struct {
	Size        ImageSize   `json:"size"`
	Format      ImageFormat `json:"format,omitempty"`
	Quality     int         `json:"quality,omitempty"` // 1-100
	DeviceType  DeviceType  `json:"device_type,omitempty"`
	EnableWebP  bool        `json:"enable_webp,omitempty"`
	EnableAVIF  bool        `json:"enable_avif,omitempty"`
	LazyLoading bool        `json:"lazy_loading,omitempty"`
	Placeholder bool        `json:"placeholder,omitempty"`
}

// OptimizedImageURL represents an optimized image URL with metadata
type OptimizedImageURL struct {
	URL         string            `json:"url"`
	WebPURL     string            `json:"webp_url,omitempty"`
	AVIFURL     string            `json:"avif_url,omitempty"`
	Width       int               `json:"width"`
	Height      int               `json:"height"`
	Format      ImageFormat       `json:"format"`
	Size        ImageSize         `json:"size"`
	CacheMaxAge int               `json:"cache_max_age"`
	Headers     map[string]string `json:"headers"`
}

// ResponsiveImageSet represents a set of images for responsive design
type ResponsiveImageSet struct {
	Default  OptimizedImageURL   `json:"default"`
	Variants []OptimizedImageURL `json:"variants"`
	Srcset   string              `json:"srcset"`
	Sizes    string              `json:"sizes"`
	LazyLoad bool                `json:"lazy_load"`
}

// NewImageServingService creates a new image serving service
func NewImageServingService() *ImageServingService {
	// Use MinIO/S3 direct URLs
	endpoint := os.Getenv("MINIO_ENDPOINT")
	bucket := os.Getenv("MINIO_BUCKET")
	useSSL := os.Getenv("MINIO_USE_SSL") == "true"

	protocol := "http"
	if useSSL {
		protocol = "https"
	}
	storageBaseURL := fmt.Sprintf("%s://%s/%s", protocol, endpoint, bucket)

	cacheMaxAge := 86400 * 7 // 7 days default for local serving
	if maxAge := os.Getenv("IMAGE_CACHE_MAX_AGE"); maxAge != "" {
		// Parse custom cache duration if needed
	}

	return &ImageServingService{
		storageBaseURL: storageBaseURL,
		cacheMaxAge:    cacheMaxAge,
	}
}

// GetBaseURL returns the storage base URL
func (s *ImageServingService) GetBaseURL() string {
	return s.storageBaseURL
}

// GenerateOptimizedURL generates an optimized image URL based on options
func (s *ImageServingService) GenerateOptimizedURL(imagePath string, options ImageServingOptions) OptimizedImageURL {
	baseURL := s.storageBaseURL

	// Build the image URL with size variant
	imageURL := s.buildImageURL(baseURL, imagePath, options.Size, options.Format)

	// Generate WebP variant if supported
	webpURL := ""
	if options.EnableWebP {
		webpURL = s.buildImageURL(baseURL, imagePath, options.Size, FormatWebP)
	}

	// Generate AVIF variant if supported
	avifURL := ""
	if options.EnableAVIF {
		avifURL = s.buildImageURL(baseURL, imagePath, options.Size, FormatAVIF)
	}

	// Get dimensions for the size
	width, height := s.getDimensionsForSize(options.Size)

	// Generate cache headers
	headers := s.generateCacheHeaders(options)

	return OptimizedImageURL{
		URL:         imageURL,
		WebPURL:     webpURL,
		AVIFURL:     avifURL,
		Width:       width,
		Height:      height,
		Format:      options.Format,
		Size:        options.Size,
		CacheMaxAge: s.cacheMaxAge,
		Headers:     headers,
	}
}

// GenerateResponsiveImageSet generates a complete responsive image set
func (s *ImageServingService) GenerateResponsiveImageSet(imagePath string, options ImageServingOptions) ResponsiveImageSet {
	// Generate variants for different sizes
	sizes := []ImageSize{SizeThumbnail, SizeSmall, SizeMedium, SizeLarge}
	variants := make([]OptimizedImageURL, 0, len(sizes))

	var defaultImage OptimizedImageURL

	for _, size := range sizes {
		sizeOptions := options
		sizeOptions.Size = size

		optimizedURL := s.GenerateOptimizedURL(imagePath, sizeOptions)
		variants = append(variants, optimizedURL)

		// Use medium as default
		if size == SizeMedium {
			defaultImage = optimizedURL
		}
	}

	// If no medium size, use the first variant as default
	if defaultImage.URL == "" && len(variants) > 0 {
		defaultImage = variants[0]
	}

	// Generate srcset string for responsive images
	srcset := s.generateSrcset(variants)

	// Generate sizes attribute for responsive images
	sizesAttr := s.generateSizesAttribute(options.DeviceType)

	return ResponsiveImageSet{
		Default:  defaultImage,
		Variants: variants,
		Srcset:   srcset,
		Sizes:    sizesAttr,
		LazyLoad: options.LazyLoading,
	}
}

// buildImageURL constructs the full image URL with size and format for MinIO storage
func (s *ImageServingService) buildImageURL(baseURL, imagePath string, size ImageSize, format ImageFormat) string {
	// For MinIO, we use the storage path directly with size variants
	// The imagePath should already include the products/ prefix from storage

	// Extract the base filename and extension
	parts := strings.Split(imagePath, ".")
	if len(parts) < 2 {
		return fmt.Sprintf("%s/%s", baseURL, imagePath)
	}

	baseName := strings.Join(parts[:len(parts)-1], ".")
	originalExt := parts[len(parts)-1]

	// Build the filename with size suffix
	var filename string
	if size == SizeOriginal {
		filename = fmt.Sprintf("%s_original.%s", baseName, originalExt)
	} else {
		filename = fmt.Sprintf("%s_%s.%s", baseName, string(size), originalExt)
	}

	// If specific format is requested and different from original
	if format != "" && string(format) != originalExt {
		// Replace extension with requested format
		parts := strings.Split(filename, ".")
		if len(parts) >= 2 {
			filename = fmt.Sprintf("%s.%s", strings.Join(parts[:len(parts)-1], "."), string(format))
		}
	}

	return fmt.Sprintf("%s/%s", baseURL, filename)
}

// getDimensionsForSize returns width and height for a given size
func (s *ImageServingService) getDimensionsForSize(size ImageSize) (int, int) {
	switch size {
	case SizeThumbnail:
		return 150, 150
	case SizeSmall:
		return 300, 300
	case SizeMedium:
		return 600, 600
	case SizeLarge:
		return 1200, 1200
	default:
		return 1200, 1200 // Default to large
	}
}

// generateCacheHeaders creates appropriate cache headers for local serving
func (s *ImageServingService) generateCacheHeaders(options ImageServingOptions) map[string]string {
	headers := make(map[string]string)

	// Cache control headers - shorter cache for local serving
	headers["Cache-Control"] = fmt.Sprintf("public, max-age=%d", s.cacheMaxAge)
	headers["Expires"] = time.Now().Add(time.Duration(s.cacheMaxAge) * time.Second).Format(time.RFC1123)

	// Content type based on format
	if options.Format != "" {
		headers["Content-Type"] = fmt.Sprintf("image/%s", string(options.Format))
	}

	// CORS headers for local API
	headers["Access-Control-Allow-Origin"] = "*"
	headers["Access-Control-Allow-Methods"] = "GET, HEAD, OPTIONS"
	headers["Access-Control-Allow-Headers"] = "Accept, Accept-Encoding"

	// Performance headers
	headers["X-Content-Type-Options"] = "nosniff"
	headers["Vary"] = "Accept, Accept-Encoding"

	return headers
}

// generateSrcset creates a srcset string for responsive images
func (s *ImageServingService) generateSrcset(variants []OptimizedImageURL) string {
	var srcsetParts []string

	for _, variant := range variants {
		srcsetParts = append(srcsetParts, fmt.Sprintf("%s %dw", variant.URL, variant.Width))
	}

	return strings.Join(srcsetParts, ", ")
}

// generateSizesAttribute creates a sizes attribute for responsive images
func (s *ImageServingService) generateSizesAttribute(deviceType DeviceType) string {
	switch deviceType {
	case DeviceMobile:
		return "(max-width: 768px) 100vw, (max-width: 1024px) 50vw, 33vw"
	case DeviceTablet:
		return "(max-width: 1024px) 50vw, 33vw"
	case DeviceDesktop:
		return "(max-width: 1200px) 50vw, 33vw"
	default:
		return "(max-width: 768px) 100vw, (max-width: 1024px) 50vw, 33vw"
	}
}

// GeneratePlaceholderURL generates a placeholder image URL for lazy loading
func (s *ImageServingService) GeneratePlaceholderURL(width, height int) string {
	// Generate a simple SVG placeholder
	svg := fmt.Sprintf(`<svg width="%d" height="%d" viewBox="0 0 %d %d" fill="none" xmlns="http://www.w3.org/2000/svg"><rect width="%d" height="%d" fill="#F3F4F6"/><text x="50%%" y="50%%" text-anchor="middle" dy=".3em" fill="#9CA3AF" font-family="Arial, sans-serif" font-size="14">%dx%d</text></svg>`,
		width, height, width, height, width, height, width, height)

	// Encode to base64
	encoded := base64.StdEncoding.EncodeToString([]byte(svg))

	return fmt.Sprintf("data:image/svg+xml;base64,%s", encoded)
}

// GetOptimalFormat determines the best image format based on browser support
func (s *ImageServingService) GetOptimalFormat(acceptHeader string) ImageFormat {
	acceptHeader = strings.ToLower(acceptHeader)

	// Check for AVIF support (most modern)
	if strings.Contains(acceptHeader, "image/avif") {
		return FormatAVIF
	}

	// Check for WebP support (widely supported)
	if strings.Contains(acceptHeader, "image/webp") {
		return FormatWebP
	}

	// Fallback to JPEG
	return FormatJPEG
}
