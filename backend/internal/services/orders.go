package services

import (
	"database/sql"
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"github.com/anandjewels/jewelry-backend/internal/database"
	"github.com/anandjewels/jewelry-backend/internal/models"
	"github.com/google/uuid"
)

// OrderService handles business logic for orders
type OrderService struct {
	db              *database.DB
	productService  *ProductService
	customerService *CustomerService
}

// NewOrderService creates a new order service
func NewOrderService(db *database.DB) *OrderService {
	return &OrderService{
		db:              db,
		productService:  NewProductService(db),
		customerService: NewCustomerService(db),
	}
}

// CreateOrder creates a new order
func (s *OrderService) CreateOrder(req *models.CreateOrderRequest) (*models.Order, error) {
	// Start transaction
	tx, err := s.db.Postgres.Begin()
	if err != nil {
		return nil, fmt.Errorf("failed to start transaction: %w", err)
	}
	defer tx.Rollback()

	// Verify customer exists
	_, err = s.customerService.GetCustomerByID(req.CustomerID)
	if err != nil {
		return nil, fmt.Errorf("customer not found: %w", err)
	}

	// Calculate total amount
	var totalAmount float64
	var orderItems []models.OrderItem

	for _, item := range req.Items {
		// Get product details
		product, err := s.productService.GetProductByID(item.ProductID)
		if err != nil {
			return nil, fmt.Errorf("product not found: %w", err)
		}

		// Check stock availability
		if product.StockQuantity < item.Quantity {
			return nil, fmt.Errorf("insufficient stock for product %s", product.Name)
		}

		// Create order item
		orderItem := models.OrderItem{
			ID:            uuid.New(),
			ProductID:     item.ProductID,
			Quantity:      item.Quantity,
			UnitPrice:     product.Price,
			TotalPrice:    product.Price * float64(item.Quantity),
			ProductName:   product.Name,
			ProductSKU:    product.SKU,
			ProductWeight: product.Weight,
			CreatedAt:     time.Now(),
		}

		orderItems = append(orderItems, orderItem)
		totalAmount += orderItem.TotalPrice
	}

	// Calculate final amount
	finalAmount := totalAmount - req.DiscountAmount

	// Create order
	order := &models.Order{
		ID:             uuid.New(),
		OrderNumber:    models.GenerateOrderNumber(),
		CustomerID:     req.CustomerID,
		Status:         models.OrderStatusPending,
		TotalAmount:    totalAmount,
		DiscountAmount: req.DiscountAmount,
		FinalAmount:    finalAmount,
		DeliveryDate:   req.DeliveryDate,
		Notes:          req.Notes,
		InternalNotes:  req.InternalNotes,
		CreatedAt:      time.Now(),
		UpdatedAt:      time.Now(),
	}

	// Marshal delivery address to JSON if provided
	if req.DeliveryAddress != nil {
		addressJSON, err := json.Marshal(req.DeliveryAddress)
		if err != nil {
			return nil, fmt.Errorf("failed to marshal delivery address: %w", err)
		}
		order.DeliveryAddress = addressJSON
	}

	// Get customer details for the order
	customer, err := s.customerService.GetCustomerByID(req.CustomerID)
	if err != nil {
		return nil, fmt.Errorf("failed to get customer details: %w", err)
	}

	// Insert order
	orderQuery := `
		INSERT INTO orders (
			id, order_number, customer_id, customer_name, customer_phone, customer_email,
			customer_address, subtotal, discount_amount, total_amount, status,
			special_instructions, admin_notes, created_at, updated_at
		) VALUES (
			$1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15
		)`

	_, err = tx.Exec(orderQuery,
		order.ID, order.OrderNumber, order.CustomerID, customer.Name, customer.Phone, customer.Email,
		customer.Address, order.TotalAmount, order.DiscountAmount, finalAmount, order.Status,
		order.Notes, order.InternalNotes, order.CreatedAt, order.UpdatedAt,
	)

	if err != nil {
		return nil, fmt.Errorf("failed to create order: %w", err)
	}

	// Insert order items and update stock
	for _, item := range orderItems {
		item.OrderID = order.ID

		itemQuery := `
			INSERT INTO order_items (
				id, order_id, product_id, quantity, unit_price, total_price,
				product_name, product_sku, product_weight, created_at
			) VALUES (
				$1, $2, $3, $4, $5, $6, $7, $8, $9, $10
			)`

		_, err = tx.Exec(itemQuery,
			item.ID, item.OrderID, item.ProductID, item.Quantity,
			item.UnitPrice, item.TotalPrice, item.ProductName,
			item.ProductSKU, item.ProductWeight, item.CreatedAt,
		)

		if err != nil {
			return nil, fmt.Errorf("failed to create order item: %w", err)
		}

		// Update product stock
		stockQuery := "UPDATE products SET stock_quantity = stock_quantity - $1 WHERE id = $2"
		_, err = tx.Exec(stockQuery, item.Quantity, item.ProductID)
		if err != nil {
			return nil, fmt.Errorf("failed to update product stock: %w", err)
		}

		// Log inventory change
		logQuery := `
			INSERT INTO inventory_logs (id, product_id, change_type, quantity_change, reason, created_at)
			VALUES ($1, $2, 'decrease', $3, $4, $5)
		`
		_, err = tx.Exec(logQuery, uuid.New(), item.ProductID, -item.Quantity,
			fmt.Sprintf("Order %s", order.OrderNumber), time.Now())
		if err != nil {
			return nil, fmt.Errorf("failed to log inventory change: %w", err)
		}
	}

	// Update customer statistics
	customerUpdateQuery := `
		UPDATE customers
		SET total_orders = total_orders + 1,
			total_spent = total_spent + $1,
			last_order_date = $2,
			updated_at = $3
		WHERE id = $4
	`
	_, err = tx.Exec(customerUpdateQuery, finalAmount, order.CreatedAt, time.Now(), req.CustomerID)
	if err != nil {
		return nil, fmt.Errorf("failed to update customer statistics: %w", err)
	}

	// Commit transaction
	if err = tx.Commit(); err != nil {
		return nil, fmt.Errorf("failed to commit transaction: %w", err)
	}

	order.Items = orderItems
	return order, nil
}

// GetOrders retrieves orders with filtering and pagination
func (s *OrderService) GetOrders(req *models.OrderListRequest) ([]*models.Order, int, error) {
	// Build WHERE clause
	var conditions []string
	var args []interface{}
	argIndex := 1

	if req.CustomerID != nil {
		conditions = append(conditions, fmt.Sprintf("o.customer_id = $%d", argIndex))
		args = append(args, *req.CustomerID)
		argIndex++
	}

	if req.Status != nil {
		conditions = append(conditions, fmt.Sprintf("o.status = $%d", argIndex))
		args = append(args, *req.Status)
		argIndex++
	}

	if req.Search != nil && *req.Search != "" {
		conditions = append(conditions, fmt.Sprintf("(o.order_number ILIKE $%d OR c.name ILIKE $%d OR c.email ILIKE $%d)", argIndex, argIndex, argIndex))
		args = append(args, "%"+*req.Search+"%")
		argIndex++
	}

	if req.DateFrom != nil {
		conditions = append(conditions, fmt.Sprintf("o.created_at >= $%d", argIndex))
		args = append(args, *req.DateFrom)
		argIndex++
	}

	if req.DateTo != nil {
		conditions = append(conditions, fmt.Sprintf("o.created_at <= $%d", argIndex))
		args = append(args, *req.DateTo)
		argIndex++
	}

	whereClause := ""
	if len(conditions) > 0 {
		whereClause = "WHERE " + strings.Join(conditions, " AND ")
	}

	// Count total records
	countQuery := fmt.Sprintf(`
		SELECT COUNT(*)
		FROM orders o
		LEFT JOIN customers c ON o.customer_id = c.id
		%s
	`, whereClause)

	var total int
	err := s.db.Postgres.QueryRow(countQuery, args...).Scan(&total)
	if err != nil {
		return nil, 0, fmt.Errorf("failed to count orders: %w", err)
	}

	// Build ORDER BY clause
	orderBy := "o.created_at DESC"
	if req.SortBy != "" {
		validSortFields := map[string]bool{
			"order_number": true,
			"total_amount": true,
			"created_at":   true,
			"updated_at":   true,
		}
		if validSortFields[req.SortBy] {
			direction := "ASC"
			if req.SortOrder == "desc" {
				direction = "DESC"
			}
			orderBy = fmt.Sprintf("o.%s %s", req.SortBy, direction)
		}
	}

	// Calculate offset
	offset := (req.Page - 1) * req.Limit

	// Query orders with customer information
	query := fmt.Sprintf(`
		SELECT o.id, o.order_number, o.customer_id, o.status, o.total_amount,
			   o.discount_amount, o.customer_address, o.special_instructions,
			   o.admin_notes, o.created_at, o.updated_at,
			   c.name, c.email, c.phone, c.address,
			   c.total_orders, c.total_spent, c.last_order_at, c.notes,
			   c.created_at as customer_created_at, c.updated_at as customer_updated_at
		FROM orders o
		LEFT JOIN customers c ON o.customer_id = c.id
		%s
		ORDER BY %s
		LIMIT $%d OFFSET $%d
	`, whereClause, orderBy, argIndex, argIndex+1)

	args = append(args, req.Limit, offset)

	rows, err := s.db.Postgres.Query(query, args...)
	if err != nil {
		return nil, 0, fmt.Errorf("failed to query orders: %w", err)
	}
	defer rows.Close()

	var orders []*models.Order
	for rows.Next() {
		order := &models.Order{}
		customer := &models.Customer{}
		var customerName, customerEmail, customerPhone sql.NullString
		var customerTotalOrders, customerTotalSpent sql.NullInt64
		var customerLastOrderDate sql.NullTime
		var customerNotes sql.NullString
		var customerCreatedAt, customerUpdatedAt sql.NullTime
		var customerAddress sql.NullString

		err := rows.Scan(
			&order.ID, &order.OrderNumber, &order.CustomerID, &order.Status,
			&order.TotalAmount, &order.DiscountAmount, &order.DeliveryAddress,
			&order.Notes, &order.InternalNotes, &order.CreatedAt, &order.UpdatedAt,
			&customerName, &customerEmail, &customerPhone, &customerAddress,
			&customerTotalOrders, &customerTotalSpent, &customerLastOrderDate, &customerNotes,
			&customerCreatedAt, &customerUpdatedAt,
		)
		if err != nil {
			return nil, 0, fmt.Errorf("failed to scan order: %w", err)
		}

		// Calculate final amount
		order.FinalAmount = order.TotalAmount - order.DiscountAmount

		// Set customer fields with null handling
		customer.ID = order.CustomerID
		if customerName.Valid {
			customer.Name = customerName.String
		}
		if customerEmail.Valid {
			customer.Email = customerEmail.String
		}
		if customerPhone.Valid {
			customer.Phone = customerPhone.String
		}
		if customerAddress.Valid {
			customer.Address = []byte(customerAddress.String)
		}
		if customerTotalOrders.Valid {
			customer.TotalOrders = int(customerTotalOrders.Int64)
		}
		if customerTotalSpent.Valid {
			customer.TotalSpent = float64(customerTotalSpent.Int64)
		}
		if customerLastOrderDate.Valid {
			customer.LastOrderDate = &customerLastOrderDate.Time
		}
		if customerNotes.Valid {
			customer.Notes = &customerNotes.String
		}
		if customerCreatedAt.Valid {
			customer.CreatedAt = customerCreatedAt.Time
		}
		if customerUpdatedAt.Valid {
			customer.UpdatedAt = customerUpdatedAt.Time
		}

		customer.IsActive = true
		customer.PreferredMethod = "phone"
		order.Customer = customer
		orders = append(orders, order)
	}

	if err = rows.Err(); err != nil {
		return nil, 0, fmt.Errorf("error iterating orders: %w", err)
	}

	return orders, total, nil
}

// GetOrderByID retrieves a single order by ID with items
func (s *OrderService) GetOrderByID(id uuid.UUID) (*models.Order, error) {
	// Get order with customer
	query := `
		SELECT o.id, o.order_number, o.customer_id, o.status, o.total_amount,
			   o.discount_amount, o.customer_address, o.special_instructions,
			   o.admin_notes, o.created_at, o.updated_at,
			   c.name, c.email, c.phone, c.address,
			   c.total_orders, c.total_spent, c.last_order_at, c.notes,
			   c.created_at as customer_created_at, c.updated_at as customer_updated_at
		FROM orders o
		LEFT JOIN customers c ON o.customer_id = c.id
		WHERE o.id = $1
	`

	order := &models.Order{}
	customer := &models.Customer{}
	var customerName, customerEmail, customerPhone sql.NullString
	var customerTotalOrders, customerTotalSpent sql.NullInt64
	var customerLastOrderDate sql.NullTime
	var customerNotes sql.NullString
	var customerCreatedAt, customerUpdatedAt sql.NullTime
	var customerAddress sql.NullString

	err := s.db.Postgres.QueryRow(query, id).Scan(
		&order.ID, &order.OrderNumber, &order.CustomerID, &order.Status,
		&order.TotalAmount, &order.DiscountAmount, &order.DeliveryAddress,
		&order.Notes, &order.InternalNotes, &order.CreatedAt, &order.UpdatedAt,
		&customerName, &customerEmail, &customerPhone, &customerAddress,
		&customerTotalOrders, &customerTotalSpent, &customerLastOrderDate, &customerNotes,
		&customerCreatedAt, &customerUpdatedAt,
	)

	if err != nil {
		return nil, err
	}

	// Calculate final amount
	order.FinalAmount = order.TotalAmount - order.DiscountAmount

	// Set customer fields with null handling
	customer.ID = order.CustomerID
	if customerName.Valid {
		customer.Name = customerName.String
	}
	if customerEmail.Valid {
		customer.Email = customerEmail.String
	}
	if customerPhone.Valid {
		customer.Phone = customerPhone.String
	}
	if customerAddress.Valid {
		customer.Address = []byte(customerAddress.String)
	}
	if customerTotalOrders.Valid {
		customer.TotalOrders = int(customerTotalOrders.Int64)
	}
	if customerTotalSpent.Valid {
		customer.TotalSpent = float64(customerTotalSpent.Int64)
	}
	if customerLastOrderDate.Valid {
		customer.LastOrderDate = &customerLastOrderDate.Time
	}
	if customerNotes.Valid {
		customer.Notes = &customerNotes.String
	}
	if customerCreatedAt.Valid {
		customer.CreatedAt = customerCreatedAt.Time
	}
	if customerUpdatedAt.Valid {
		customer.UpdatedAt = customerUpdatedAt.Time
	}

	customer.IsActive = true
	customer.PreferredMethod = "phone"
	order.Customer = customer

	// Get order items
	itemsQuery := `
		SELECT oi.id, oi.product_id, oi.quantity, oi.unit_price, oi.total_price,
			   oi.product_name, oi.product_sku, oi.product_weight, oi.created_at,
			   p.name, p.sku, p.description, p.price, p.category, p.subcategory,
			   p.weight, p.material, p.gemstone, p.metal_purity, p.dimensions,
			   p.availability, p.stock_quantity, p.tags, p.is_featured, p.is_active,
			   p.created_at as product_created_at, p.updated_at as product_updated_at
		FROM order_items oi
		LEFT JOIN products p ON oi.product_id = p.id
		WHERE oi.order_id = $1
		ORDER BY oi.created_at ASC
	`

	itemRows, err := s.db.Postgres.Query(itemsQuery, id)
	if err != nil {
		return nil, fmt.Errorf("failed to query order items: %w", err)
	}
	defer itemRows.Close()

	var items []models.OrderItem
	for itemRows.Next() {
		item := models.OrderItem{}
		product := &models.Product{}

		err := itemRows.Scan(
			&item.ID, &item.ProductID, &item.Quantity, &item.UnitPrice,
			&item.TotalPrice, &item.ProductName, &item.ProductSKU,
			&item.ProductWeight, &item.CreatedAt,
			&product.Name, &product.SKU, &product.Description, &product.Price,
			&product.Category, &product.Subcategory, &product.Weight,
			&product.Material, &product.Gemstone, &product.MetalPurity,
			&product.Dimensions, &product.Availability, &product.StockQuantity,
			&product.Tags, &product.IsFeatured, &product.IsActive,
			&product.CreatedAt, &product.UpdatedAt,
		)
		if err != nil {
			return nil, fmt.Errorf("failed to scan order item: %w", err)
		}

		product.ID = item.ProductID
		item.OrderID = order.ID
		item.Product = product
		items = append(items, item)
	}

	if err = itemRows.Err(); err != nil {
		return nil, fmt.Errorf("error iterating order items: %w", err)
	}

	order.Items = items
	return order, nil
}

// UpdateOrderStatus updates the status of an order
func (s *OrderService) UpdateOrderStatus(id uuid.UUID, status string) (*models.Order, error) {
	// Check if order exists
	var exists bool
	err := s.db.Postgres.QueryRow("SELECT EXISTS(SELECT 1 FROM orders WHERE id = $1)", id).Scan(&exists)
	if err != nil {
		return nil, err
	}
	if !exists {
		return nil, fmt.Errorf("order not found")
	}

	// Update order status
	_, err = s.db.Postgres.Exec("UPDATE orders SET status = $1, updated_at = NOW() WHERE id = $2", status, id)
	if err != nil {
		return nil, fmt.Errorf("failed to update order status: %w", err)
	}

	// Return updated order
	return s.GetOrderByID(id)
}
