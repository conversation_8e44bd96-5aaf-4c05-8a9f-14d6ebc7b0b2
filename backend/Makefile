# Makefile for Jewelry Backend

.PHONY: help test test-verbose test-coverage test-unit test-integration clean build run dev deps lint fmt vet

# Default target
help:
	@echo "Available targets:"
	@echo "  test           - Run all tests"
	@echo "  test-verbose   - Run tests with verbose output"
	@echo "  test-coverage  - Run tests with coverage report"
	@echo "  test-unit      - Run unit tests only"
	@echo "  test-integration - Run integration tests only"
	@echo "  clean          - Clean build artifacts and test cache"
	@echo "  build          - Build the application"
	@echo "  run            - Run the application"
	@echo "  dev            - Run in development mode with hot reload"
	@echo "  deps           - Download and tidy dependencies"
	@echo "  lint           - Run linter"
	@echo "  fmt            - Format code"
	@echo "  vet            - Run go vet"

# Test targets
test:
	@echo "Running all tests..."
	go test ./tests/... -v

test-verbose:
	@echo "Running tests with verbose output..."
	go test ./tests/... -v -race

test-coverage:
	@echo "Running tests with coverage..."
	go test ./tests/... -v -race -coverprofile=coverage.out
	go tool cover -html=coverage.out -o coverage.html
	@echo "Coverage report generated: coverage.html"

test-unit:
	@echo "Running unit tests..."
	go test ./tests/... -v -run "Test.*TestSuite" -short

test-integration:
	@echo "Running integration tests..."
	go test ./tests/... -v -run "TestIntegration.*"

# Development targets
clean:
	@echo "Cleaning..."
	go clean
	go clean -testcache
	rm -f coverage.out coverage.html

build:
	@echo "Building application..."
	go build -o bin/jewelry-backend ./cmd/main.go

run: build
	@echo "Running application..."
	./bin/jewelry-backend

dev:
	@echo "Running in development mode..."
	go run ./cmd/main.go

# Dependency management
deps:
	@echo "Downloading dependencies..."
	go mod download
	go mod tidy

# Code quality
lint:
	@echo "Running linter..."
	golangci-lint run

fmt:
	@echo "Formatting code..."
	go fmt ./...

vet:
	@echo "Running go vet..."
	go vet ./...

# Database migrations (if using migrate tool)
migrate-up:
	@echo "Running database migrations..."
	migrate -path migrations -database "postgres://postgres:password@localhost:5432/jewelry_db?sslmode=disable" up

migrate-down:
	@echo "Rolling back database migrations..."
	migrate -path migrations -database "postgres://postgres:password@localhost:5432/jewelry_db?sslmode=disable" down

# Docker targets
docker-build:
	@echo "Building Docker image..."
	docker build -t jewelry-backend .

docker-run:
	@echo "Running Docker container..."
	docker run -p 8080:8080 jewelry-backend

# Test database setup (for CI/CD)
test-db-setup:
	@echo "Setting up test database..."
	# This would set up a test database for integration tests
	# Implementation depends on your CI/CD environment

# All quality checks
check: fmt vet lint test
	@echo "All checks passed!"

# Install development tools
install-tools:
	@echo "Installing development tools..."
	go install github.com/golangci/golangci-lint/cmd/golangci-lint@latest
	go install -tags 'postgres' github.com/golang-migrate/migrate/v4/cmd/migrate@latest
