# Database Configuration
DB_HOST=localhost
DB_PORT=5432
DB_NAME=jewelry_db
DB_USER=postgres
DB_PASSWORD=password
DB_SSL_MODE=disable

# Redis Configuration
REDIS_URL=redis://localhost:6379
REDIS_PASSWORD=

# Server Configuration
PORT=8080
GIN_MODE=release
CORS_ORIGINS=http://localhost:3000

# Authentication
GOOGLE_CLIENT_ID=your_google_client_id
GOOGLE_CLIENT_SECRET=your_google_client_secret
JWT_SECRET=your_super_secret_jwt_key_change_this_in_production
JWT_EXPIRY_HOURS=24

# Image Storage (Choose one)
# Cloudinary
CLOUDINARY_CLOUD_NAME=your_cloud_name
CLOUDINARY_API_KEY=your_api_key
CLOUDINARY_API_SECRET=your_api_secret

# AWS S3 (Alternative)
AWS_REGION=us-east-1
AWS_ACCESS_KEY_ID=your_access_key
AWS_SECRET_ACCESS_KEY=your_secret_key
S3_BUCKET_NAME=jewelry-images

# Application Settings
MAX_UPLOAD_SIZE=10485760  # 10MB in bytes
ALLOWED_IMAGE_TYPES=jpg,jpeg,png,webp
IMAGE_QUALITY=85

# Logging
LOG_LEVEL=info
LOG_FORMAT=json