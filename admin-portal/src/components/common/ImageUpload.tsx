import { useState, useCallback } from 'react';
import { useDropzone } from 'react-dropzone';
import {
  PhotoIcon,
  XMarkIcon,
  ArrowUpTrayIcon,
  EyeIcon,
} from '@heroicons/react/24/outline';

interface ImageFile {
  id: string;
  file?: File;
  url: string;
  name: string;
  size?: number;
  isUploading?: boolean;
  error?: string;
}

interface ImageUploadProps {
  images: ImageFile[];
  onImagesChange: (images: ImageFile[]) => void;
  maxImages?: number;
  maxSizeInMB?: number;
  acceptedTypes?: string[];
  className?: string;
}

const ImageUpload: React.FC<ImageUploadProps> = ({
  images,
  onImagesChange,
  maxImages = 10,
  maxSizeInMB = 5,
  acceptedTypes = ['image/jpeg', 'image/png', 'image/webp'],
  className = '',
}) => {
  const [draggedIndex, setDraggedIndex] = useState<number | null>(null);

  const onDrop = useCallback((acceptedFiles: File[]) => {
    const newImages: ImageFile[] = acceptedFiles.map((file) => {
      // Validate file
      const allowedTypes = ['image/jpeg', 'image/png', 'image/webp'];
      let error: string | undefined;

      if (!allowedTypes.includes(file.type)) {
        error = 'Only JPEG, PNG, and WebP images are allowed';
      } else if (file.size > maxSizeInMB * 1024 * 1024) {
        error = `File size exceeds ${maxSizeInMB}MB`;
      }

      return {
        id: `${Date.now()}-${Math.random()}`,
        file,
        url: URL.createObjectURL(file),
        name: file.name,
        size: file.size,
        isUploading: false,
        error,
      };
    });

    // Filter out invalid images and respect max count
    const validImages = newImages.filter(img => !img.error);
    const totalImages = images.length + validImages.length;
    if (totalImages > maxImages) {
      const allowedCount = maxImages - images.length;
      validImages.splice(allowedCount);
    }

    // Include invalid images to show errors
    const invalidImages = newImages.filter(img => img.error);
    onImagesChange([...images, ...validImages, ...invalidImages]);
  }, [images, onImagesChange, maxImages, maxSizeInMB]);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: acceptedTypes.reduce((acc, type) => ({ ...acc, [type]: [] }), {}),
    multiple: true,
    disabled: images.length >= maxImages,
  });

  const removeImage = (id: string) => {
    const updatedImages = images.filter((img) => img.id !== id);
    onImagesChange(updatedImages);
  };

  const moveImage = (fromIndex: number, toIndex: number) => {
    const updatedImages = [...images];
    const [movedImage] = updatedImages.splice(fromIndex, 1);
    updatedImages.splice(toIndex, 0, movedImage);
    onImagesChange(updatedImages);
  };

  const handleDragStart = (index: number) => {
    setDraggedIndex(index);
  };

  const handleDragOver = (e: React.DragEvent, index: number) => {
    e.preventDefault();
    if (draggedIndex !== null && draggedIndex !== index) {
      moveImage(draggedIndex, index);
      setDraggedIndex(index);
    }
  };

  const handleDragEnd = () => {
    setDraggedIndex(null);
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Upload Area */}
      {images.length < maxImages && (
        <div
          {...getRootProps()}
          className={`border-2 border-dashed rounded-lg p-6 text-center cursor-pointer transition-colors ${
            isDragActive
              ? 'border-primary-500 bg-primary-50'
              : 'border-gray-300 hover:border-gray-400'
          }`}
        >
          <input {...getInputProps()} />
          <PhotoIcon className="mx-auto h-12 w-12 text-gray-400" />
          <div className="mt-4">
            <p className="text-sm font-medium text-gray-900">
              {isDragActive ? 'Drop images here' : 'Upload product images'}
            </p>
            <p className="text-sm text-gray-500 mt-1">
              Drag and drop or click to select files
            </p>
            <p className="text-xs text-gray-400 mt-2">
              PNG, JPG, WebP up to {maxSizeInMB}MB each (max {maxImages} images)
            </p>
          </div>
        </div>
      )}

      {/* Image Grid */}
      {images.length > 0 && (
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
          {images.map((image, index) => (
            <div
              key={image.id}
              draggable
              onDragStart={() => handleDragStart(index)}
              onDragOver={(e) => handleDragOver(e, index)}
              onDragEnd={handleDragEnd}
              className={`relative group bg-white border rounded-lg overflow-hidden shadow-sm hover:shadow-md transition-shadow cursor-move ${
                draggedIndex === index ? 'opacity-50' : ''
              }`}
            >
              {/* Image */}
              <div className="aspect-square bg-gray-100">
                <img
                  src={image.url}
                  alt={image.name}
                  className="w-full h-full object-cover"
                />
              </div>

              {/* Overlay */}
              <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-40 transition-all duration-200 flex items-center justify-center">
                <div className="opacity-0 group-hover:opacity-100 flex space-x-2">
                  <button
                    type="button"
                    onClick={() => window.open(image.url, '_blank')}
                    className="p-2 bg-white rounded-full text-gray-700 hover:text-gray-900 transition-colors"
                    title="View full size"
                  >
                    <EyeIcon className="h-4 w-4" />
                  </button>
                  <button
                    type="button"
                    onClick={() => removeImage(image.id)}
                    className="p-2 bg-white rounded-full text-red-600 hover:text-red-700 transition-colors"
                    title="Remove image"
                  >
                    <XMarkIcon className="h-4 w-4" />
                  </button>
                </div>
              </div>

              {/* Primary badge */}
              {index === 0 && (
                <div className="absolute top-2 left-2">
                  <span className="bg-primary-600 text-white text-xs px-2 py-1 rounded">
                    Primary
                  </span>
                </div>
              )}

              {/* Upload status */}
              {image.isUploading && (
                <div className="absolute inset-0 bg-white bg-opacity-90 flex items-center justify-center">
                  <div className="text-center">
                    <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary-600 mx-auto"></div>
                    <p className="text-xs text-gray-600 mt-1">Processing...</p>
                  </div>
                </div>
              )}

              {/* Error state */}
              {image.error && (
                <div className="absolute inset-0 bg-red-50 bg-opacity-90 flex items-center justify-center">
                  <div className="text-center p-2">
                    <XMarkIcon className="h-6 w-6 text-red-600 mx-auto" />
                    <p className="text-xs text-red-600 mt-1">{image.error}</p>
                  </div>
                </div>
              )}

              {/* Image info */}
              <div className="p-2 bg-white">
                <p className="text-xs text-gray-600 truncate" title={image.name}>
                  {image.name}
                </p>
                {image.size && (
                  <p className="text-xs text-gray-400">
                    {formatFileSize(image.size)}
                  </p>
                )}
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Instructions */}
      {images.length > 0 && (
        <div className="text-xs text-gray-500 bg-gray-50 p-3 rounded">
          <p>• Drag images to reorder them</p>
          <p>• The first image will be used as the primary product image</p>
          <p>• Click the eye icon to view full size</p>
        </div>
      )}
    </div>
  );
};

export default ImageUpload;
