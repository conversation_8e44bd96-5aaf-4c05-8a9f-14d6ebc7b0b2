import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { useQuery } from '@tanstack/react-query';
import { MagnifyingGlassIcon } from '@heroicons/react/24/outline';
import { getProducts } from '../../lib/api';
import ProductImageDisplay from '../products/ProductImageDisplay';
import type { Collection, CreateCollectionRequest, UpdateCollectionRequest, CollectionFormData, Product, FilterOptions } from '../../types';

const collectionSchema = z.object({
  name: z.string().min(1, 'Collection name is required'),
  description: z.string().min(1, 'Description is required'),
  is_public: z.boolean(),
  expires_at_date: z.string().optional(),
  expires_at_time: z.string().optional(),
});

interface CollectionFormProps {
  initialData?: Collection;
  onSubmit: (data: CreateCollectionRequest | UpdateCollectionRequest, selectedProducts?: string[]) => void;
  onCancel: () => void;
  isLoading?: boolean;
  error?: Error | null;
}

const CollectionForm: React.FC<CollectionFormProps> = ({
  initialData,
  onSubmit,
  onCancel,
  isLoading = false,
  error,
}) => {
  // Product selection state
  const [selectedProducts, setSelectedProducts] = useState<string[]>(() => {
    // Initialize with existing products if editing
    return initialData?.products?.map(p => p.id) || [];
  });
  const [productSearch, setProductSearch] = useState('');
  const [productFilters] = useState<FilterOptions>({
    page: 1,
    limit: 50, // Show more products for selection
    search: productSearch,
    category: '',
    is_featured: undefined,
    is_active: true, // Only show active products
  });
  // Fetch products for selection
  const { data: productsData } = useQuery({
    queryKey: ['products', { ...productFilters, search: productSearch }],
    queryFn: () => getProducts({ ...productFilters, search: productSearch }),
  });

  const {
    register,
    handleSubmit,
    formState: { errors },
    watch,
    setValue,
  } = useForm<CollectionFormData>({
    resolver: zodResolver(collectionSchema),
    defaultValues: initialData ? {
      name: initialData.name,
      description: initialData.description,
      is_public: initialData.is_public,
      expires_at_date: initialData.expires_at ? initialData.expires_at.split('T')[0] : '',
      expires_at_time: initialData.expires_at ? initialData.expires_at.split('T')[1]?.split('.')[0] : '',
    } : {
      name: '',
      description: '',
      is_public: true,
      expires_at_date: '',
      expires_at_time: '',
    },
  });

  // Product selection helpers
  const handleProductToggle = (productId: string) => {
    setSelectedProducts(prev =>
      prev.includes(productId)
        ? prev.filter(id => id !== productId)
        : [...prev, productId]
    );
  };

  const handleSelectAll = () => {
    if (productsData?.data) {
      const allProductIds = productsData.data.map(p => p.id);
      setSelectedProducts(allProductIds);
    }
  };

  const handleDeselectAll = () => {
    setSelectedProducts([]);
  };

  const handleFormSubmit = (data: CollectionFormData) => {
    let expires_at: string | undefined;

    if (data.expires_at_date && data.expires_at_time) {
      expires_at = `${data.expires_at_date}T${data.expires_at_time}:00.000Z`;
    } else if (data.expires_at_date) {
      expires_at = `${data.expires_at_date}T23:59:59.000Z`;
    }

    // Auto-generate slug from name (still needed for backend compatibility)
    const slug = data.name
      .toLowerCase()
      .replace(/[^a-z0-9\s-]/g, '')
      .replace(/\s+/g, '-')
      .replace(/-+/g, '-')
      .trim();

    const submitData: CreateCollectionRequest | UpdateCollectionRequest = {
      slug,
      name: data.name,
      description: data.description,
      is_public: data.is_public,
      expires_at,
    };

    onSubmit(submitData, selectedProducts);
  };

  return (
    <form onSubmit={handleSubmit(handleFormSubmit)} className="space-y-6">
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-md p-4">
          <div className="text-red-800">
            Failed to save collection. Please try again.
          </div>
        </div>
      )}

      <div className="space-y-8">
        {/* Basic Information */}
        <div className="space-y-4">
          <h3 className="text-lg font-medium text-gray-900">Basic Information</h3>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
            <div>
              <label className="form-label">Collection Name *</label>
              <input
                type="text"
                className="form-input"
                {...register('name')}
              />
              {errors.name && <p className="form-error">{errors.name.message}</p>}
            </div>

            <div className="flex items-center">
              <input
                type="checkbox"
                className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                {...register('is_public')}
              />
              <label className="ml-2 block text-sm text-gray-900">
                Public Collection
              </label>
            </div>
          </div>

          <div>
            <label className="form-label">Description *</label>
            <textarea
              rows={4}
              className="form-input"
              {...register('description')}
            />
            {errors.description && <p className="form-error">{errors.description.message}</p>}
          </div>
        </div>

        {/* Product Selection */}
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-medium text-gray-900">Select Products</h3>
            <div className="text-sm text-gray-500">
              {selectedProducts.length} product{selectedProducts.length !== 1 ? 's' : ''} selected
            </div>
          </div>

          {/* Product Search */}
          <div className="relative">
            <MagnifyingGlassIcon className="h-5 w-5 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
            <input
              type="text"
              placeholder="Search products..."
              className="form-input pl-10"
              value={productSearch}
              onChange={(e) => setProductSearch(e.target.value)}
            />
          </div>

          {/* Selection Controls */}
          <div className="flex gap-2">
            <button
              type="button"
              onClick={handleSelectAll}
              className="btn-outline text-xs"
              disabled={!productsData?.data?.length}
            >
              Select All
            </button>
            <button
              type="button"
              onClick={handleDeselectAll}
              className="btn-outline text-xs"
              disabled={selectedProducts.length === 0}
            >
              Clear Selection
            </button>
          </div>

          {/* Product Grid */}
          <div className="max-h-96 overflow-y-auto border border-gray-200 rounded-lg">
            {productsData?.data?.length ? (
              <div className="grid grid-cols-1 gap-2 p-4">
                {productsData.data.map((product) => (
                  <div
                    key={product.id}
                    className={`flex items-center p-3 border rounded-lg cursor-pointer transition-colors ${
                      selectedProducts.includes(product.id)
                        ? 'border-primary-500 bg-primary-50'
                        : 'border-gray-200 hover:border-gray-300'
                    }`}
                    onClick={() => handleProductToggle(product.id)}
                  >
                    <input
                      type="checkbox"
                      checked={selectedProducts.includes(product.id)}
                      onChange={(e) => {
                        e.stopPropagation();
                        handleProductToggle(product.id);
                      }}
                      onClick={(e) => e.stopPropagation()}
                      className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded mr-3"
                    />
                    <div className="flex-shrink-0 mr-3">
                      <ProductImageDisplay
                        images={product.images}
                        productName={product.name}
                        size="sm"
                        showGalleryButton={false}
                      />
                    </div>
                    <div className="flex-1 min-w-0">
                      <div className="text-sm font-medium text-gray-900 truncate">
                        {product.name}
                      </div>
                      <div className="text-sm text-gray-500">
                        SKU: {product.sku} • ₹{product.price.toLocaleString()}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="p-8 text-center text-gray-500">
                {productSearch ? 'No products found matching your search.' : 'No products available.'}
              </div>
            )}
          </div>
        </div>

        {/* Expiration Settings */}
        <div className="space-y-4">
          <h3 className="text-lg font-medium text-gray-900">Expiration (Optional)</h3>

          <div>
            <label className="form-label">Expiration Date</label>
            <div className="grid grid-cols-2 gap-2">
              <div>
                <input
                  type="date"
                  className="form-input"
                  {...register('expires_at_date')}
                />
                <p className="text-xs text-gray-500 mt-1">Date</p>
              </div>
              <div>
                <input
                  type="time"
                  className="form-input"
                  {...register('expires_at_time')}
                />
                <p className="text-xs text-gray-500 mt-1">Time (optional)</p>
              </div>
            </div>
            <p className="text-xs text-gray-500 mt-1">
              Leave empty for collections that never expire
            </p>
          </div>


        </div>
      </div>

      {/* Form Actions */}
      <div className="flex justify-end space-x-4 pt-6 border-t">
        <button
          type="button"
          onClick={onCancel}
          className="btn-outline"
          disabled={isLoading}
        >
          Cancel
        </button>
        <button
          type="submit"
          className="btn-primary"
          disabled={isLoading}
        >
          {isLoading ? 'Saving...' : initialData ? 'Update Collection' : 'Create Collection'}
        </button>
      </div>
    </form>
  );
};

export default CollectionForm;
