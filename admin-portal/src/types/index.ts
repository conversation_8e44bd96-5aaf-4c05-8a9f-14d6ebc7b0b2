// API Response Types
export interface ProductImage {
  id: string;
  product_id: string;
  image_url: string;
  thumbnail_url?: string;
  medium_url?: string;
  large_url?: string;
  alt_text: string;
  display_order: number;
  is_primary: boolean;
  file_size?: number;
  width?: number;
  height?: number;
  created_at: string;
}

export interface Product {
  id: string;
  sku: string;
  name: string;
  description: string;
  price: number;
  cost_price?: number;
  category: string;
  subcategory?: string;
  weight?: number;
  material?: string;
  gemstone?: string;
  metal_purity?: string;
  dimensions?: Dimensions;
  availability: string;
  stock_quantity: number;
  min_stock_level: number;
  tags: string[];
  is_featured: boolean;
  is_active: boolean;
  images?: ProductImage[];
  created_at: string;
  updated_at: string;
}

export interface Collection {
  id: string;
  slug: string;
  name: string;
  description: string;
  cover_image_url?: string;
  is_active: boolean;
  is_public: boolean;
  expires_at?: string;
  view_count: number;
  share_count: number;
  created_at: string;
  updated_at: string;
  products?: Product[];
  product_count?: number;
}

export interface Customer {
  id: string;
  name: string;
  email?: string;
  phone: string;
  address?: {
    street?: string;
    city?: string;
    state?: string;
    country?: string;
    pincode?: string;
  };
  total_orders: number;
  total_spent: number;
  last_order_date?: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export interface Order {
  id: string;
  order_number: string;
  customer_id: string;
  status: 'pending' | 'confirmed' | 'processing' | 'shipped' | 'delivered' | 'cancelled';
  total_amount: number;
  discount_amount: number;
  final_amount: number;
  delivery_address: {
    street: string;
    city: string;
    state: string;
    country: string;
    pincode?: string;
  };
  notes?: string;
  created_at: string;
  updated_at: string;
  customer?: Customer;
  items?: OrderItem[];
}

export interface OrderItem {
  id: string;
  product_id: string;
  quantity: number;
  unit_price: number;
  total_price: number;
  product?: Product;
}

export interface InventoryStatus {
  product_id: string;
  product_name: string;
  product_sku: string;
  current_stock: number;
  min_stock_level: number;
  is_low_stock: boolean;
  is_out_of_stock: boolean;
  last_updated: string;
}

// Request Types
export interface Dimensions {
  length: number;
  width: number;
  height: number;
  unit: string; // "mm", "cm", "inch"
}

export interface CreateProductRequest {
  sku: string;
  name: string;
  description: string;
  price: number;
  cost_price?: number;
  category: string;
  subcategory?: string;
  weight?: number;
  material?: string;
  gemstone?: string;
  metal_purity?: string;
  dimensions?: Dimensions;
  stock_quantity: number;
  min_stock_level: number;
  tags: string[];
  is_featured: boolean;
  images?: Omit<ProductImage, 'id'>[]; // Using mock upload for demonstration
}

export interface CreateCollectionRequest {
  slug: string;
  name: string;
  description: string;
  cover_image_url?: string;
  is_public: boolean;
  expires_at?: string;
}

export interface UpdateCollectionRequest {
  slug?: string;
  name?: string;
  description?: string;
  cover_image_url?: string;
  is_public?: boolean;
  expires_at?: string;
}

export interface CreateCustomerRequest {
  name: string;
  email?: string;
  phone: string;
  address?: {
    street?: string;
    city?: string;
    state?: string;
    country?: string;
    pincode?: string;
  };
}

export interface CreateOrderRequest {
  customer_id: string;
  items: {
    product_id: string;
    quantity: number;
  }[];
  delivery_address: {
    street: string;
    city: string;
    state: string;
    country: string;
    pincode?: string;
  };
  discount_amount?: number;
  notes?: string;
}

// API Response Wrappers
export interface PaginatedResponse<T> {
  data: T[];
  total: number;
  page: number;
  limit: number;
  total_pages: number;
}

export interface ApiResponse<T> {
  data: T;
  message?: string;
}

export interface ApiError {
  error: string;
  message: string;
  code: number;
  details?: string;
}

// UI State Types
export interface DashboardStats {
  total_products: number;
  total_collections: number;
  total_orders: number;
  total_customers: number;
  pending_orders: number;
  low_stock_products: number;
  revenue_today: number;
  revenue_month: number;
}

export interface FilterOptions {
  search?: string;
  category?: string;
  status?: string;
  is_active?: boolean;
  is_featured?: boolean;
  is_public?: boolean;
  stock_level?: string;
  date_from?: string;
  date_to?: string;
  page?: number;
  limit?: number;
}

// Form Types
export interface ProductFormData extends Omit<CreateProductRequest, 'tags' | 'dimensions'> {
  tags: string; // Comma-separated string for form input
  dimensions_length?: number;
  dimensions_width?: number;
  dimensions_height?: number;
  dimensions_unit?: string;
}

export interface CollectionFormData extends Omit<CreateCollectionRequest, 'slug'> {
  expires_at_date?: string;
  expires_at_time?: string;
}

// Activity types
export type ActivityType =
  | 'order_created'
  | 'order_updated'
  | 'product_created'
  | 'product_updated'
  | 'product_deleted'
  | 'collection_created'
  | 'collection_updated'
  | 'inventory_updated'
  | 'customer_created'
  | 'low_stock_alert';

export interface Activity {
  id: string;
  type: ActivityType;
  title: string;
  description: string;
  entity_type: string; // 'product', 'order', 'collection', 'customer', 'inventory'
  entity_id?: string;
  entity_name?: string;
  user_id?: string;
  user_name?: string;
  metadata?: Record<string, any>;
  created_at: string;
}

export interface ActivityListRequest {
  page?: number;
  limit?: number;
  type?: ActivityType;
  entity_type?: string;
  start_date?: string;
  end_date?: string;
}

// Navigation Types
export interface NavItem {
  name: string;
  href: string;
  icon: React.ComponentType<{ className?: string }>;
  current?: boolean;
  badge?: number;
}

export interface BreadcrumbItem {
  name: string;
  href?: string;
  current?: boolean;
}
