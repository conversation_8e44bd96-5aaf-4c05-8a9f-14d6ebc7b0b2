import React, { useState, useEffect } from 'react';
import { useSearchParams, Link } from 'react-router-dom';
import { useQuery } from '@tanstack/react-query';
import {
  MagnifyingGlassIcon,
  CubeIcon,
  RectangleStackIcon,
  ShoppingBagIcon,
  UsersIcon,
} from '@heroicons/react/24/outline';
import { unifiedSearch } from '../lib/api';

const SearchPage: React.FC = () => {
  const [searchParams, setSearchParams] = useSearchParams();
  const [query, setQuery] = useState(searchParams.get('q') || '');
  const [selectedType, setSelectedType] = useState<string>('all');

  // Update query from URL params
  useEffect(() => {
    const urlQuery = searchParams.get('q') || '';
    setQuery(urlQuery);
  }, [searchParams]);

  // Search results
  const { data: searchResults, isLoading, error } = useQuery({
    queryKey: ['unified-search-page', query],
    queryFn: () => unifiedSearch(query, 50),
    enabled: query.length > 0,
  });

  // Filter results by type
  const filteredResults = searchResults?.results.filter(result => 
    selectedType === 'all' || result.type === selectedType
  ) || [];

  // Get result counts by type
  const resultCounts = searchResults?.results.reduce((acc, result) => {
    acc[result.type] = (acc[result.type] || 0) + 1;
    return acc;
  }, {} as Record<string, number>) || {};

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    if (query.trim()) {
      setSearchParams({ q: query.trim() });
    }
  };

  const getIcon = (type: string) => {
    switch (type) {
      case 'product':
        return CubeIcon;
      case 'collection':
        return RectangleStackIcon;
      case 'order':
        return ShoppingBagIcon;
      case 'customer':
        return UsersIcon;
      default:
        return MagnifyingGlassIcon;
    }
  };

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'product':
        return 'bg-blue-100 text-blue-800';
      case 'collection':
        return 'bg-purple-100 text-purple-800';
      case 'order':
        return 'bg-green-100 text-green-800';
      case 'customer':
        return 'bg-yellow-100 text-yellow-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const typeFilters = [
    { key: 'all', label: 'All Results', count: searchResults?.results.length || 0 },
    { key: 'product', label: 'Products', count: resultCounts.product || 0 },
    { key: 'collection', label: 'Collections', count: resultCounts.collection || 0 },
    { key: 'order', label: 'Orders', count: resultCounts.order || 0 },
    { key: 'customer', label: 'Customers', count: resultCounts.customer || 0 },
  ];

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div>
        <h1 className="text-2xl font-bold text-gray-900">Search</h1>
        <p className="mt-1 text-sm text-gray-500">
          Search across products, collections, orders, and customers
        </p>
      </div>

      {/* Search Form */}
      <div className="card p-6">
        <form onSubmit={handleSearch} className="flex gap-4">
          <div className="flex-1 relative">
            <div className="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
              <MagnifyingGlassIcon className="h-5 w-5 text-gray-400" />
            </div>
            <input
              type="search"
              value={query}
              onChange={(e) => setQuery(e.target.value)}
              placeholder="Search products, orders, customers..."
              className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
            />
          </div>
          <button
            type="submit"
            className="btn-primary"
          >
            Search
          </button>
        </form>
      </div>

      {/* Results */}
      {query && (
        <div className="space-y-6">
          {/* Type Filters */}
          <div className="flex flex-wrap gap-2">
            {typeFilters.map((filter) => (
              <button
                key={filter.key}
                onClick={() => setSelectedType(filter.key)}
                className={`inline-flex items-center px-3 py-1.5 rounded-full text-sm font-medium transition-colors ${
                  selectedType === filter.key
                    ? 'bg-primary-100 text-primary-800 border border-primary-200'
                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200 border border-gray-200'
                }`}
              >
                {filter.label}
                {filter.count > 0 && (
                  <span className="ml-1.5 bg-white bg-opacity-50 rounded-full px-1.5 py-0.5 text-xs">
                    {filter.count}
                  </span>
                )}
              </button>
            ))}
          </div>

          {/* Loading State */}
          {isLoading && (
            <div className="text-center py-12">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600 mx-auto"></div>
              <p className="mt-4 text-gray-500">Searching...</p>
            </div>
          )}

          {/* Error State */}
          {error && (
            <div className="text-center py-12">
              <p className="text-red-600">An error occurred while searching. Please try again.</p>
            </div>
          )}

          {/* Results */}
          {!isLoading && !error && (
            <>
              {filteredResults.length > 0 ? (
                <div className="space-y-4">
                  <div className="text-sm text-gray-500">
                    Showing {filteredResults.length} of {searchResults?.total || 0} results for "{query}"
                  </div>
                  
                  <div className="space-y-3">
                    {filteredResults.map((result) => {
                      const Icon = getIcon(result.type);
                      return (
                        <Link
                          key={`${result.type}-${result.id}`}
                          to={result.url}
                          className="block card p-4 hover:shadow-md transition-shadow"
                        >
                          <div className="flex items-start space-x-4">
                            <div className="flex-shrink-0 mt-1">
                              <Icon className="h-6 w-6 text-gray-400" />
                            </div>
                            <div className="flex-1 min-w-0">
                              <div className="flex items-center space-x-2 mb-1">
                                <h3 className="text-lg font-medium text-gray-900 truncate">
                                  {result.title}
                                </h3>
                                <span className={`inline-flex items-center px-2 py-0.5 rounded text-xs font-medium ${getTypeColor(result.type)}`}>
                                  {result.type}
                                </span>
                              </div>
                              {result.subtitle && (
                                <p className="text-sm text-gray-600 mb-1">
                                  {result.subtitle}
                                </p>
                              )}
                              {result.description && (
                                <p className="text-sm text-gray-500">
                                  {result.description}
                                </p>
                              )}
                              {result.metadata && (
                                <div className="mt-2 flex flex-wrap gap-2">
                                  {Object.entries(result.metadata).map(([key, value]) => (
                                    <span
                                      key={key}
                                      className="inline-flex items-center px-2 py-0.5 rounded text-xs bg-gray-100 text-gray-600"
                                    >
                                      {key}: {String(value)}
                                    </span>
                                  ))}
                                </div>
                              )}
                            </div>
                            {result.image_url && (
                              <div className="flex-shrink-0">
                                <img
                                  src={result.image_url}
                                  alt=""
                                  className="h-16 w-16 rounded object-cover"
                                />
                              </div>
                            )}
                          </div>
                        </Link>
                      );
                    })}
                  </div>
                </div>
              ) : searchResults?.results.length === 0 ? (
                <div className="text-center py-12">
                  <MagnifyingGlassIcon className="h-12 w-12 mx-auto text-gray-300 mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">No results found</h3>
                  <p className="text-gray-500">
                    No results found for "{query}". Try adjusting your search terms.
                  </p>
                </div>
              ) : null}
            </>
          )}
        </div>
      )}

      {/* Empty State */}
      {!query && (
        <div className="text-center py-12">
          <MagnifyingGlassIcon className="h-12 w-12 mx-auto text-gray-300 mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">Start searching</h3>
          <p className="text-gray-500">
            Enter a search term above to find products, collections, orders, and customers.
          </p>
        </div>
      )}
    </div>
  );
};

export default SearchPage;
