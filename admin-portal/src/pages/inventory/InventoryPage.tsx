import React, { useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import {
  MagnifyingGlassIcon,
  FunnelIcon,
  ExclamationTriangleIcon,
  PlusIcon,
  MinusIcon,
  ChartBarIcon,
  ClockIcon,
} from '@heroicons/react/24/outline';
import { getInventoryStatus, getLowStockAlerts, getOutOfStockAlerts } from '../../lib/api';
import type { FilterOptions, InventoryStatus } from '../../types';
import InventoryFilters from '../../components/inventory/InventoryFilters';
import InventoryAdjustModal from '../../components/inventory/InventoryAdjustModal';
import InventoryAlerts from '../../components/inventory/InventoryAlerts';

const InventoryPage: React.FC = () => {
  const [filters, setFilters] = useState<FilterOptions>({
    page: 1,
    limit: 20,
    search: '',
  });
  const [showFilters, setShowFilters] = useState(false);
  const [adjustItem, setAdjustItem] = useState<InventoryStatus | null>(null);
  const [activeTab, setActiveTab] = useState<'all' | 'low-stock' | 'out-of-stock'>('all');

  const { data: inventoryData, isLoading, error, refetch } = useQuery({
    queryKey: ['inventory', filters],
    queryFn: () => getInventoryStatus(filters),
  });

  const { data: lowStockAlerts } = useQuery({
    queryKey: ['low-stock-alerts'],
    queryFn: getLowStockAlerts,
  });

  const { data: outOfStockAlerts } = useQuery({
    queryKey: ['out-of-stock-alerts'],
    queryFn: getOutOfStockAlerts,
  });

  const handleSearch = (search: string) => {
    setFilters(prev => ({ ...prev, search, page: 1 }));
  };

  const handleFilterChange = (newFilters: Partial<FilterOptions>) => {
    setFilters(prev => ({ ...prev, ...newFilters, page: 1 }));
  };

  const handlePageChange = (page: number) => {
    setFilters(prev => ({ ...prev, page }));
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-IN', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const getStockStatus = (item: InventoryStatus) => {
    if (item.is_out_of_stock) {
      return { class: 'badge-danger', label: 'Out of Stock' };
    }
    if (item.is_low_stock) {
      return { class: 'badge-warning', label: 'Low Stock' };
    }
    return { class: 'badge-success', label: 'In Stock' };
  };

  const getStockLevel = (current: number, min: number) => {
    const percentage = min > 0 ? (current / min) * 100 : 100;
    if (current === 0) return 0;
    if (percentage <= 100) return Math.max(percentage, 10); // Minimum 10% for visibility
    return 100;
  };

  const getStockLevelColor = (current: number, min: number) => {
    if (current === 0) return 'bg-red-500';
    if (current <= min) return 'bg-yellow-500';
    return 'bg-green-500';
  };

  const filteredData = React.useMemo(() => {
    if (!inventoryData?.data) return [];
    
    switch (activeTab) {
      case 'low-stock':
        return inventoryData.data.filter(item => item.is_low_stock && !item.is_out_of_stock);
      case 'out-of-stock':
        return inventoryData.data.filter(item => item.is_out_of_stock);
      default:
        return inventoryData.data;
    }
  }, [inventoryData?.data, activeTab]);

  if (error) {
    return (
      <div className="text-center py-12">
        <div className="text-red-600 mb-4">Error loading inventory</div>
        <button onClick={() => refetch()} className="btn-primary">
          Try Again
        </button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Page header */}
      <div className="sm:flex sm:items-center sm:justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Inventory Management</h1>
          <p className="mt-1 text-sm text-gray-500">
            Monitor stock levels and manage inventory adjustments
          </p>
        </div>
        <div className="mt-4 sm:mt-0 flex space-x-3">
          <div className="text-sm text-gray-500">
            Total Items: {inventoryData?.total || 0}
          </div>
        </div>
      </div>

      {/* Alerts */}
      <InventoryAlerts 
        lowStockAlerts={lowStockAlerts || []}
        outOfStockAlerts={outOfStockAlerts || []}
      />

      {/* Tabs */}
      <div className="border-b border-gray-200">
        <nav className="-mb-px flex space-x-8">
          <button
            onClick={() => setActiveTab('all')}
            className={`py-2 px-1 border-b-2 font-medium text-sm ${
              activeTab === 'all'
                ? 'border-primary-500 text-primary-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            All Items ({inventoryData?.total || 0})
          </button>
          <button
            onClick={() => setActiveTab('low-stock')}
            className={`py-2 px-1 border-b-2 font-medium text-sm ${
              activeTab === 'low-stock'
                ? 'border-yellow-500 text-yellow-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            Low Stock ({lowStockAlerts?.length || 0})
          </button>
          <button
            onClick={() => setActiveTab('out-of-stock')}
            className={`py-2 px-1 border-b-2 font-medium text-sm ${
              activeTab === 'out-of-stock'
                ? 'border-red-500 text-red-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            Out of Stock ({outOfStockAlerts?.length || 0})
          </button>
        </nav>
      </div>

      {/* Search and filters */}
      <div className="card p-4">
        <div className="flex flex-col sm:flex-row gap-4">
          {/* Search */}
          <div className="flex-1">
            <div className="relative">
              <MagnifyingGlassIcon className="h-5 w-5 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
              <input
                type="text"
                placeholder="Search products by name or SKU..."
                className="form-input pl-10"
                value={filters.search}
                onChange={(e) => handleSearch(e.target.value)}
              />
            </div>
          </div>

          {/* Filter toggle */}
          <button
            onClick={() => setShowFilters(!showFilters)}
            className="btn-outline"
          >
            <FunnelIcon className="h-4 w-4 mr-2" />
            Filters
          </button>
        </div>

        {/* Filters */}
        {showFilters && (
          <div className="mt-4 pt-4 border-t">
            <InventoryFilters
              filters={filters}
              onFilterChange={handleFilterChange}
            />
          </div>
        )}
      </div>

      {/* Inventory table */}
      <div className="card overflow-hidden">
        {isLoading ? (
          <div className="p-8 text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600 mx-auto"></div>
            <p className="mt-2 text-gray-500">Loading inventory...</p>
          </div>
        ) : (
          <>
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="table-header">Product</th>
                    <th className="table-header">Current Stock</th>
                    <th className="table-header">Min Level</th>
                    <th className="table-header">Stock Level</th>
                    <th className="table-header">Status</th>
                    <th className="table-header">Last Updated</th>
                    <th className="table-header">Actions</th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {filteredData.map((item) => {
                    const status = getStockStatus(item);
                    const stockLevel = getStockLevel(item.current_stock, item.min_stock_level);
                    const stockColor = getStockLevelColor(item.current_stock, item.min_stock_level);
                    
                    return (
                      <tr key={item.product_id} className="hover:bg-gray-50">
                        <td className="table-cell">
                          <div>
                            <div className="text-sm font-medium text-gray-900">
                              {item.product_name}
                            </div>
                            <div className="text-sm text-gray-500">
                              SKU: {item.product_sku}
                            </div>
                          </div>
                        </td>
                        <td className="table-cell">
                          <div className="text-sm font-medium text-gray-900">
                            {item.current_stock}
                          </div>
                        </td>
                        <td className="table-cell">
                          <div className="text-sm text-gray-900">
                            {item.min_stock_level}
                          </div>
                        </td>
                        <td className="table-cell">
                          <div className="w-full bg-gray-200 rounded-full h-2">
                            <div
                              className={`h-2 rounded-full ${stockColor}`}
                              style={{ width: `${stockLevel}%` }}
                            ></div>
                          </div>
                          <div className="text-xs text-gray-500 mt-1">
                            {item.current_stock > 0 
                              ? `${Math.round((item.current_stock / Math.max(item.min_stock_level, 1)) * 100)}%`
                              : '0%'
                            }
                          </div>
                        </td>
                        <td className="table-cell">
                          <span className={`badge ${status.class}`}>
                            {status.label}
                          </span>
                        </td>
                        <td className="table-cell">
                          <div className="text-sm text-gray-900 flex items-center">
                            <ClockIcon className="h-4 w-4 mr-1 text-gray-400" />
                            {formatDate(item.last_updated)}
                          </div>
                        </td>
                        <td className="table-cell">
                          <div className="flex items-center space-x-2">
                            <button
                              onClick={() => setAdjustItem(item)}
                              className="text-primary-600 hover:text-primary-700"
                              title="Adjust inventory"
                            >
                              <ChartBarIcon className="h-4 w-4" />
                            </button>
                          </div>
                        </td>
                      </tr>
                    );
                  })}
                </tbody>
              </table>
            </div>

            {/* Empty state */}
            {filteredData.length === 0 && !isLoading && (
              <div className="text-center py-12">
                <div className="text-gray-500 mb-4">
                  {activeTab === 'all' ? 'No inventory items found' : 
                   activeTab === 'low-stock' ? 'No low stock items' : 
                   'No out of stock items'}
                </div>
                <p className="text-sm text-gray-400">
                  {activeTab === 'all' ? 'Inventory items will appear here when products are added' :
                   'Items will appear here when stock levels need attention'}
                </p>
              </div>
            )}

            {/* Pagination */}
            {inventoryData && inventoryData.total_pages > 1 && (
              <div className="bg-white px-4 py-3 border-t border-gray-200 sm:px-6">
                <div className="flex items-center justify-between">
                  <div className="text-sm text-gray-700">
                    Showing {((filters.page || 1) - 1) * (filters.limit || 20) + 1} to{' '}
                    {Math.min((filters.page || 1) * (filters.limit || 20), inventoryData.total)} of{' '}
                    {inventoryData.total} results
                  </div>
                  <div className="flex space-x-2">
                    <button
                      onClick={() => handlePageChange((filters.page || 1) - 1)}
                      disabled={filters.page === 1}
                      className="btn-outline disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      Previous
                    </button>
                    <button
                      onClick={() => handlePageChange((filters.page || 1) + 1)}
                      disabled={filters.page === inventoryData.total_pages}
                      className="btn-outline disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      Next
                    </button>
                  </div>
                </div>
              </div>
            )}
          </>
        )}
      </div>

      {/* Inventory adjustment modal */}
      {adjustItem && (
        <InventoryAdjustModal
          item={adjustItem}
          onClose={() => setAdjustItem(null)}
          onSuccess={() => {
            setAdjustItem(null);
            refetch();
          }}
        />
      )}
    </div>
  );
};

export default InventoryPage;
