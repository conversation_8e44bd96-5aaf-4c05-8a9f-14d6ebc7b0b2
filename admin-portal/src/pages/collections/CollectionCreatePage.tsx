import React from 'react';
import { useNavigate } from 'react-router-dom';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { ArrowLeftIcon } from '@heroicons/react/24/outline';
import { createCollection, addProductToCollection } from '../../lib/api';
import CollectionForm from '../../components/collections/CollectionForm';
import type { CreateCollectionRequest } from '../../types';

const CollectionCreatePage: React.FC = () => {
  const navigate = useNavigate();
  const queryClient = useQueryClient();

  const createMutation = useMutation({
    mutationFn: createCollection,
    onSuccess: (data) => {
      // Invalidate collections cache
      queryClient.invalidateQueries({ queryKey: ['collections'] });

      // Navigate to the collections list
      navigate('/collections', {
        state: { message: `Collection "${data.name}" created successfully!` }
      });
    },
    onError: (error) => {
      console.error('Failed to create collection:', error);
      // You could add toast notification here
    },
  });

  const handleSubmit = async (data: CreateCollectionRequest, selectedProducts?: string[]) => {
    try {
      // Create the collection first
      const collection = await createMutation.mutateAsync(data);

      // Add products to collection if any are selected
      if (selectedProducts && selectedProducts.length > 0) {
        await Promise.all(
          selectedProducts.map((productId, index) =>
            addProductToCollection(collection.id, productId, index)
          )
        );
      }
    } catch (error) {
      console.error('Failed to create collection:', error);
    }
  };

  const handleCancel = () => {
    navigate('/collections');
  };

  return (
    <div className="space-y-6">
      {/* Page header */}
      <div className="flex items-center space-x-4">
        <button
          onClick={() => navigate('/collections')}
          className="text-gray-400 hover:text-gray-500"
        >
          <ArrowLeftIcon className="h-6 w-6" />
        </button>
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Create Collection</h1>
          <p className="mt-1 text-sm text-gray-500">
            Create a new jewelry collection to showcase your products
          </p>
        </div>
      </div>

      {/* Collection form */}
      <div className="card p-6">
        <CollectionForm
          onSubmit={handleSubmit}
          onCancel={handleCancel}
          isLoading={createMutation.isPending}
          error={createMutation.error}
        />
      </div>
    </div>
  );
};

export default CollectionCreatePage;
