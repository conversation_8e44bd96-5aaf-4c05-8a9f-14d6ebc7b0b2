import React, { useState } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { useQuery } from '@tanstack/react-query';
import {
  ArrowLeftIcon,
  PhoneIcon,
  MapPinIcon,
  CalendarIcon,
  CurrencyRupeeIcon,
  PencilIcon,
} from '@heroicons/react/24/outline';
import { getOrder } from '../../lib/api';
import OrderStatusModal from '../../components/orders/OrderStatusModal';

const OrderDetailPage: React.FC = () => {
  const navigate = useNavigate();
  const { id } = useParams<{ id: string }>();
  const [showStatusModal, setShowStatusModal] = useState(false);

  const { data: order, isLoading, error, refetch } = useQuery({
    queryKey: ['order', id],
    queryFn: () => getOrder(id!),
    enabled: !!id,
  });

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
    }).format(price);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-IN', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const getStatusBadge = (status: string) => {
    const statusConfig: Record<string, { class: string; label: string }> = {
      pending: { class: 'badge-warning', label: 'Pending' },
      confirmed: { class: 'badge-info', label: 'Confirmed' },
      processing: { class: 'badge-info', label: 'Processing' },
      shipped: { class: 'badge-success', label: 'Shipped' },
      delivered: { class: 'badge-success', label: 'Delivered' },
      cancelled: { class: 'badge-danger', label: 'Cancelled' },
    };
    
    const config = statusConfig[status] || { class: 'badge-info', label: status };
    return <span className={`badge ${config.class}`}>{config.label}</span>;
  };

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center space-x-4">
          <button
            onClick={() => navigate('/orders')}
            className="text-gray-400 hover:text-gray-500"
          >
            <ArrowLeftIcon className="h-6 w-6" />
          </button>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Order Details</h1>
          </div>
        </div>
        <div className="card p-8 text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600 mx-auto"></div>
          <p className="mt-2 text-gray-500">Loading order...</p>
        </div>
      </div>
    );
  }

  if (error || !order) {
    return (
      <div className="space-y-6">
        <div className="flex items-center space-x-4">
          <button
            onClick={() => navigate('/orders')}
            className="text-gray-400 hover:text-gray-500"
          >
            <ArrowLeftIcon className="h-6 w-6" />
          </button>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Order Details</h1>
          </div>
        </div>
        <div className="card p-8 text-center">
          <div className="text-red-600 mb-4">
            {error ? 'Error loading order' : 'Order not found'}
          </div>
          <button onClick={() => navigate('/orders')} className="btn-primary">
            Back to Orders
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Page header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <button
            onClick={() => navigate('/orders')}
            className="text-gray-400 hover:text-gray-500"
          >
            <ArrowLeftIcon className="h-6 w-6" />
          </button>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">
              Order #{order.order_number}
            </h1>
            <p className="mt-1 text-sm text-gray-500">
              Order ID: {order.id}
            </p>
          </div>
        </div>
        <div className="flex items-center space-x-3">
          {getStatusBadge(order.status)}
          <button
            onClick={() => setShowStatusModal(true)}
            className="btn-outline"
          >
            <PencilIcon className="h-4 w-4 mr-2" />
            Update Status
          </button>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Order Summary */}
        <div className="lg:col-span-2 space-y-6">
          {/* Order Items */}
          <div className="card p-6">
            <h2 className="text-lg font-medium text-gray-900 mb-4">Order Items</h2>
            <div className="space-y-4">
              {order.items?.map((item, index) => (
                <div key={index} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                  <div className="flex items-center space-x-4">
                    <div className="h-12 w-12 bg-gray-200 rounded-lg flex items-center justify-center">
                      <span className="text-xs text-gray-500">IMG</span>
                    </div>
                    <div>
                      <div className="font-medium text-gray-900">
                        {item.product?.name || 'Unknown Product'}
                      </div>
                      <div className="text-sm text-gray-500">
                        SKU: {item.product?.sku || 'N/A'}
                      </div>
                      <div className="text-sm text-gray-500">
                        Quantity: {item.quantity}
                      </div>
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="font-medium text-gray-900">
                      {formatPrice(item.total_price)}
                    </div>
                    <div className="text-sm text-gray-500">
                      {formatPrice(item.unit_price)} each
                    </div>
                  </div>
                </div>
              )) || (
                <div className="text-center py-8 text-gray-500">
                  No items found for this order
                </div>
              )}
            </div>
          </div>

          {/* Order Notes */}
          {order.notes && (
            <div className="card p-6">
              <h2 className="text-lg font-medium text-gray-900 mb-4">Order Notes</h2>
              <div className="bg-gray-50 p-4 rounded-lg">
                <p className="text-gray-700">{order.notes}</p>
              </div>
            </div>
          )}
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Customer Information */}
          <div className="card p-6">
            <h2 className="text-lg font-medium text-gray-900 mb-4">Customer</h2>
            <div className="space-y-3">
              <div>
                <div className="font-medium text-gray-900">
                  {order.customer?.name || 'Unknown Customer'}
                </div>
              </div>
              {order.customer?.phone && (
                <div className="flex items-center text-sm text-gray-600">
                  <PhoneIcon className="h-4 w-4 mr-2" />
                  {order.customer.phone}
                </div>
              )}
              {order.customer?.email && (
                <div className="text-sm text-gray-600">
                  {order.customer.email}
                </div>
              )}
            </div>
          </div>

          {/* Delivery Address */}
          <div className="card p-6">
            <h2 className="text-lg font-medium text-gray-900 mb-4">Delivery Address</h2>
            <div className="flex items-start space-x-2">
              <MapPinIcon className="h-4 w-4 text-gray-400 mt-1" />
              <div className="text-sm text-gray-600">
                <div>{order.delivery_address.street}</div>
                <div>{order.delivery_address.city}, {order.delivery_address.state}</div>
                <div>{order.delivery_address.country}</div>
                {order.delivery_address.pincode && (
                  <div>{order.delivery_address.pincode}</div>
                )}
              </div>
            </div>
          </div>

          {/* Order Summary */}
          <div className="card p-6">
            <h2 className="text-lg font-medium text-gray-900 mb-4">Order Summary</h2>
            <div className="space-y-3">
              <div className="flex justify-between text-sm">
                <span className="text-gray-600">Subtotal</span>
                <span className="text-gray-900">{formatPrice(order.total_amount)}</span>
              </div>
              {order.discount_amount > 0 && (
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600">Discount</span>
                  <span className="text-red-600">-{formatPrice(order.discount_amount)}</span>
                </div>
              )}
              <div className="border-t pt-3">
                <div className="flex justify-between">
                  <span className="font-medium text-gray-900">Total</span>
                  <span className="font-medium text-gray-900">{formatPrice(order.final_amount)}</span>
                </div>
              </div>
            </div>
          </div>

          {/* Order Timeline */}
          <div className="card p-6">
            <h2 className="text-lg font-medium text-gray-900 mb-4">Order Timeline</h2>
            <div className="space-y-3">
              <div className="flex items-center text-sm">
                <CalendarIcon className="h-4 w-4 text-gray-400 mr-2" />
                <div>
                  <div className="text-gray-900">Order Created</div>
                  <div className="text-gray-500">{formatDate(order.created_at)}</div>
                </div>
              </div>
              {order.updated_at !== order.created_at && (
                <div className="flex items-center text-sm">
                  <CalendarIcon className="h-4 w-4 text-gray-400 mr-2" />
                  <div>
                    <div className="text-gray-900">Last Updated</div>
                    <div className="text-gray-500">{formatDate(order.updated_at)}</div>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Order status modal */}
      {showStatusModal && (
        <OrderStatusModal
          order={order}
          onClose={() => setShowStatusModal(false)}
          onSuccess={() => {
            setShowStatusModal(false);
            refetch();
          }}
        />
      )}
    </div>
  );
};

export default OrderDetailPage;
