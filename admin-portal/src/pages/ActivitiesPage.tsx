import React, { useState, useEffect } from 'react';
import { useQuery } from '@tanstack/react-query';
import { Link, useLocation } from 'react-router-dom';
import {
  ShoppingBagIcon,
  CubeIcon,
  RectangleStackIcon,
  UsersIcon,
  ExclamationTriangleIcon,
  ArrowTrendingUpIcon,
  ClockIcon,
  MagnifyingGlassIcon,
  FunnelIcon,
  CheckCircleIcon,
  ChevronLeftIcon,
  ChevronRightIcon,
} from '@heroicons/react/24/outline';
import { getRecentActivities } from '../lib/api';
import type { Activity, ActivityType, ActivityListRequest } from '../types';

const ActivitiesPage: React.FC = () => {
  const location = useLocation();
  const [filters, setFilters] = useState<ActivityListRequest>({
    page: 1,
    limit: 20,
    type: undefined,
    entity_type: '',
    start_date: '',
    end_date: '',
  });
  const [showFilters, setShowFilters] = useState(false);
  const [showToast, setShowToast] = useState(false);
  const [toastMessage, setToastMessage] = useState('');

  // Handle success messages from navigation state
  useEffect(() => {
    if (location.state?.message) {
      setToastMessage(location.state.message);
      setShowToast(true);
      setTimeout(() => setShowToast(false), 3000);
      // Clear the state to prevent showing the message again
      window.history.replaceState({}, document.title);
    }
  }, [location.state]);

  const { data: activitiesData, isLoading, error, refetch } = useQuery({
    queryKey: ['activities', filters],
    queryFn: () => getRecentActivities(filters),
    refetchInterval: 30000, // Refresh every 30 seconds
  });

  const getActivityIcon = (type: ActivityType) => {
    switch (type) {
      case 'order_created':
      case 'order_updated':
        return ShoppingBagIcon;
      case 'product_created':
      case 'product_updated':
      case 'product_deleted':
        return CubeIcon;
      case 'collection_created':
      case 'collection_updated':
        return RectangleStackIcon;
      case 'customer_created':
        return UsersIcon;
      case 'inventory_updated':
        return ArrowTrendingUpIcon;
      case 'low_stock_alert':
        return ExclamationTriangleIcon;
      default:
        return ClockIcon;
    }
  };

  const getActivityColor = (type: ActivityType) => {
    switch (type) {
      case 'order_created':
        return 'text-green-600 bg-green-100';
      case 'order_updated':
        return 'text-blue-600 bg-blue-100';
      case 'product_created':
      case 'collection_created':
      case 'customer_created':
        return 'text-purple-600 bg-purple-100';
      case 'product_updated':
      case 'collection_updated':
        return 'text-blue-600 bg-blue-100';
      case 'product_deleted':
        return 'text-red-600 bg-red-100';
      case 'inventory_updated':
        return 'text-indigo-600 bg-indigo-100';
      case 'low_stock_alert':
        return 'text-orange-600 bg-orange-100';
      default:
        return 'text-gray-600 bg-gray-100';
    }
  };

  const getEntityLink = (activity: Activity) => {
    switch (activity.entity_type) {
      case 'product':
        return `/products/${activity.entity_id}/edit`;
      case 'collection':
        return `/collections/${activity.entity_id}/edit`;
      case 'order':
        return `/orders/${activity.entity_id}`;
      case 'customer':
        return `/customers/${activity.entity_id}`;
      default:
        return null;
    }
  };

  const formatTimeAgo = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);

    if (diffInSeconds < 60) {
      return 'Just now';
    } else if (diffInSeconds < 3600) {
      const minutes = Math.floor(diffInSeconds / 60);
      return `${minutes} minute${minutes > 1 ? 's' : ''} ago`;
    } else if (diffInSeconds < 86400) {
      const hours = Math.floor(diffInSeconds / 3600);
      return `${hours} hour${hours > 1 ? 's' : ''} ago`;
    } else {
      const days = Math.floor(diffInSeconds / 86400);
      return `${days} day${days > 1 ? 's' : ''} ago`;
    }
  };

  const formatDateTime = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const renderActivityMetadata = (activity: Activity) => {
    if (!activity.metadata) return null;

    switch (activity.type) {
      case 'order_created':
        return (
          <div className="text-sm text-gray-500 mt-1">
            ₹{activity.metadata.order_total?.toLocaleString()} • {activity.metadata.items_count} items
          </div>
        );
      case 'inventory_updated':
        return (
          <div className="text-sm text-gray-500 mt-1">
            {activity.metadata.old_quantity} → {activity.metadata.new_quantity} units
          </div>
        );
      case 'low_stock_alert':
        return (
          <div className="text-sm text-orange-600 mt-1">
            {activity.metadata.current_stock} left (threshold: {activity.metadata.threshold})
          </div>
        );
      case 'collection_created':
        return (
          <div className="text-sm text-gray-500 mt-1">
            {activity.metadata.products_count} products
          </div>
        );
      case 'product_created':
        return (
          <div className="text-sm text-gray-500 mt-1">
            {activity.metadata.category} • ₹{activity.metadata.price?.toLocaleString()}
          </div>
        );
      default:
        return null;
    }
  };

  const handleFilterChange = (newFilters: Partial<ActivityListRequest>) => {
    setFilters(prev => ({ ...prev, ...newFilters, page: 1 }));
  };

  const handlePageChange = (page: number) => {
    setFilters(prev => ({ ...prev, page }));
  };

  const activityTypes: { value: ActivityType | ''; label: string }[] = [
    { value: '', label: 'All Types' },
    { value: 'order_created', label: 'Orders Created' },
    { value: 'order_updated', label: 'Orders Updated' },
    { value: 'product_created', label: 'Products Created' },
    { value: 'product_updated', label: 'Products Updated' },
    { value: 'product_deleted', label: 'Products Deleted' },
    { value: 'collection_created', label: 'Collections Created' },
    { value: 'collection_updated', label: 'Collections Updated' },
    { value: 'customer_created', label: 'Customers Created' },
    { value: 'inventory_updated', label: 'Inventory Updated' },
    { value: 'low_stock_alert', label: 'Low Stock Alerts' },
  ];

  const entityTypes = [
    { value: '', label: 'All Entities' },
    { value: 'product', label: 'Products' },
    { value: 'order', label: 'Orders' },
    { value: 'collection', label: 'Collections' },
    { value: 'customer', label: 'Customers' },
    { value: 'inventory', label: 'Inventory' },
  ];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Activities</h1>
          <p className="text-gray-600 mt-1">Track all business operations and system events</p>
        </div>
        <button
          onClick={() => setShowFilters(!showFilters)}
          className="btn-outline flex items-center space-x-2"
        >
          <FunnelIcon className="h-4 w-4" />
          <span>Filters</span>
        </button>
      </div>

      {/* Filters */}
      {showFilters && (
        <div className="card p-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div>
              <label className="form-label">Activity Type</label>
              <select
                className="form-input"
                value={filters.type || ''}
                onChange={(e) => handleFilterChange({ type: e.target.value as ActivityType })}
              >
                {activityTypes.map((type) => (
                  <option key={type.value} value={type.value}>
                    {type.label}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label className="form-label">Entity Type</label>
              <select
                className="form-input"
                value={filters.entity_type || ''}
                onChange={(e) => handleFilterChange({ entity_type: e.target.value })}
              >
                {entityTypes.map((entity) => (
                  <option key={entity.value} value={entity.value}>
                    {entity.label}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label className="form-label">Start Date</label>
              <input
                type="date"
                className="form-input"
                value={filters.start_date || ''}
                onChange={(e) => handleFilterChange({ start_date: e.target.value })}
              />
            </div>

            <div>
              <label className="form-label">End Date</label>
              <input
                type="date"
                className="form-input"
                value={filters.end_date || ''}
                onChange={(e) => handleFilterChange({ end_date: e.target.value })}
              />
            </div>
          </div>

          <div className="flex justify-end mt-4 space-x-3">
            <button
              onClick={() => setFilters({
                page: 1,
                limit: 20,
                type: undefined,
                entity_type: '',
                start_date: '',
                end_date: '',
              })}
              className="btn-outline"
            >
              Clear Filters
            </button>
            <button
              onClick={() => refetch()}
              className="btn-primary"
            >
              Apply Filters
            </button>
          </div>
        </div>
      )}

      {/* Activities List */}
      <div className="card">
        {isLoading ? (
          <div className="p-6">
            <div className="space-y-4">
              {[...Array(10)].map((_, i) => (
                <div key={i} className="flex items-start space-x-4 animate-pulse">
                  <div className="w-10 h-10 bg-gray-200 rounded-full"></div>
                  <div className="flex-1 space-y-2">
                    <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                    <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                    <div className="h-3 bg-gray-200 rounded w-1/4"></div>
                  </div>
                  <div className="w-20 h-3 bg-gray-200 rounded"></div>
                </div>
              ))}
            </div>
          </div>
        ) : error ? (
          <div className="p-6 text-center">
            <ExclamationTriangleIcon className="h-12 w-12 mx-auto mb-4 text-red-500" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">Failed to load activities</h3>
            <p className="text-gray-600 mb-4">There was an error loading the activities.</p>
            <button onClick={() => refetch()} className="btn-primary">
              Try Again
            </button>
          </div>
        ) : activitiesData?.data?.length ? (
          <>
            <div className="p-6">
              <div className="space-y-4">
                {activitiesData.data.map((activity) => {
                  const Icon = getActivityIcon(activity.type);
                  const colorClasses = getActivityColor(activity.type);
                  const entityLink = getEntityLink(activity);

                  return (
                    <div key={activity.id} className="flex items-start space-x-4 p-4 hover:bg-gray-50 rounded-lg transition-colors">
                      <div className={`flex-shrink-0 w-10 h-10 rounded-full flex items-center justify-center ${colorClasses}`}>
                        <Icon className="h-5 w-5" />
                      </div>
                      <div className="flex-1 min-w-0">
                        <div className="flex items-start justify-between">
                          <div className="flex-1">
                            <h3 className="text-sm font-medium text-gray-900">
                              {activity.title}
                            </h3>
                            <p className="text-sm text-gray-600 mt-1">
                              {activity.description}
                            </p>
                            {renderActivityMetadata(activity)}
                            {entityLink && (
                              <Link
                                to={entityLink}
                                className="text-sm text-primary-600 hover:text-primary-700 mt-2 inline-block"
                              >
                                View details →
                              </Link>
                            )}
                          </div>
                          <div className="text-right ml-4">
                            <p className="text-sm text-gray-500">
                              {formatTimeAgo(activity.created_at)}
                            </p>
                            <p className="text-xs text-gray-400 mt-1">
                              {formatDateTime(activity.created_at)}
                            </p>
                          </div>
                        </div>
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>

            {/* Pagination */}
            {activitiesData.total_pages > 1 && (
              <div className="border-t border-gray-200 px-6 py-4">
                <div className="flex items-center justify-between">
                  <div className="text-sm text-gray-700">
                    Showing {((activitiesData.page - 1) * activitiesData.limit) + 1} to{' '}
                    {Math.min(activitiesData.page * activitiesData.limit, activitiesData.total)} of{' '}
                    {activitiesData.total} activities
                  </div>
                  <div className="flex items-center space-x-2">
                    <button
                      onClick={() => handlePageChange(activitiesData.page - 1)}
                      disabled={activitiesData.page <= 1}
                      className="btn-outline disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-1"
                    >
                      <ChevronLeftIcon className="h-4 w-4" />
                      <span>Previous</span>
                    </button>
                    <span className="text-sm text-gray-700">
                      Page {activitiesData.page} of {activitiesData.total_pages}
                    </span>
                    <button
                      onClick={() => handlePageChange(activitiesData.page + 1)}
                      disabled={activitiesData.page >= activitiesData.total_pages}
                      className="btn-outline disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-1"
                    >
                      <span>Next</span>
                      <ChevronRightIcon className="h-4 w-4" />
                    </button>
                  </div>
                </div>
              </div>
            )}
          </>
        ) : (
          <div className="p-6 text-center">
            <ClockIcon className="h-12 w-12 mx-auto mb-4 text-gray-300" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No activities found</h3>
            <p className="text-gray-600">
              {Object.values(filters).some(v => v && v !== 1 && v !== 20) 
                ? 'Try adjusting your filters to see more activities.'
                : 'Activities will appear here as they happen.'
              }
            </p>
          </div>
        )}
      </div>

      {/* Toast notification */}
      {showToast && (
        <div className="fixed bottom-4 left-4 z-50">
          <div className="bg-green-600 text-white px-4 py-3 rounded-lg shadow-lg flex items-center space-x-2">
            <CheckCircleIcon className="h-5 w-5" />
            <span className="text-sm font-medium">{toastMessage}</span>
          </div>
        </div>
      )}
    </div>
  );
};

export default ActivitiesPage;
