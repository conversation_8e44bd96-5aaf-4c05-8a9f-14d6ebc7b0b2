import React from 'react';
import { useQuery } from '@tanstack/react-query';
import { Link } from 'react-router-dom';
import {
  CubeIcon,
  RectangleStackIcon,
  ShoppingBagIcon,
  UsersIcon,
  ExclamationTriangleIcon,
  ChartBarIcon,
} from '@heroicons/react/24/outline';
import { getDashboardStats, getLowStockAlerts } from '../lib/api';
import RecentActivity from '../components/dashboard/RecentActivity';

const Dashboard: React.FC = () => {
  const { data: stats, isLoading: statsLoading } = useQuery({
    queryKey: ['dashboard-stats'],
    queryFn: getDashboardStats,
  });

  const { data: lowStockAlerts, isLoading: alertsLoading } = useQuery({
    queryKey: ['low-stock-alerts'],
    queryFn: getLowStockAlerts,
  });

  const statCards = [
    {
      name: 'Total Products',
      value: stats?.total_products || 0,
      icon: CubeIcon,
      color: 'bg-blue-500',
      href: '/products',
    },
    {
      name: 'Collections',
      value: stats?.total_collections || 0,
      icon: RectangleStackIcon,
      color: 'bg-purple-500',
      href: '/collections',
    },
    {
      name: 'Orders',
      value: stats?.total_orders || 0,
      icon: ShoppingBagIcon,
      color: 'bg-green-500',
      href: '/orders',
    },
    {
      name: 'Customers',
      value: stats?.total_customers || 0,
      icon: UsersIcon,
      color: 'bg-yellow-500',
      href: '/customers',
    },
  ];

  const alertCards = [
    {
      name: 'Pending Orders',
      value: stats?.pending_orders || 0,
      icon: ExclamationTriangleIcon,
      color: 'bg-orange-500',
      href: '/orders?status=pending',
    },
    {
      name: 'Low Stock Items',
      value: lowStockAlerts?.length || 0,
      icon: ChartBarIcon,
      color: 'bg-red-500',
      href: '/inventory?low_stock_only=true',
    },
  ];

  if (statsLoading || alertsLoading) {
    return (
      <div className="animate-pulse">
        <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
          {[...Array(4)].map((_, i) => (
            <div key={i} className="card p-5">
              <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
              <div className="h-8 bg-gray-200 rounded w-1/2"></div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Page header */}
      <div>
        <h1 className="text-2xl font-bold text-gray-900">Dashboard</h1>
        <p className="mt-1 text-sm text-gray-500">
          Welcome to the Anand Jewels admin portal. Here's an overview of your business.
        </p>
      </div>

      {/* Stats cards */}
      <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
        {statCards.map((card) => (
          <Link
            key={card.name}
            to={card.href}
            className="card p-5 hover:shadow-lg transition-shadow duration-200"
          >
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className={`${card.color} p-3 rounded-lg`}>
                  <card.icon className="h-6 w-6 text-white" aria-hidden="true" />
                </div>
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">
                    {card.name}
                  </dt>
                  <dd className="text-lg font-medium text-gray-900">
                    {card.value.toLocaleString()}
                  </dd>
                </dl>
              </div>
            </div>
          </Link>
        ))}
      </div>

      {/* Alert cards */}
      <div className="grid grid-cols-1 gap-5 sm:grid-cols-2">
        {alertCards.map((card) => (
          <Link
            key={card.name}
            to={card.href}
            className="card p-5 hover:shadow-lg transition-shadow duration-200"
          >
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className={`${card.color} p-3 rounded-lg`}>
                  <card.icon className="h-6 w-6 text-white" aria-hidden="true" />
                </div>
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">
                    {card.name}
                  </dt>
                  <dd className="text-lg font-medium text-gray-900">
                    {card.value.toLocaleString()}
                  </dd>
                </dl>
              </div>
            </div>
          </Link>
        ))}
      </div>

      {/* Quick actions */}
      <div className="card p-6">
        <h2 className="text-lg font-medium text-gray-900 mb-4">Quick Actions</h2>
        <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-4">
          <Link
            to="/products/create"
            className="btn-primary text-center"
          >
            Add New Product
          </Link>
          <Link
            to="/collections/create"
            className="btn-outline text-center"
          >
            Create Collection
          </Link>
          <Link
            to="/customers/create"
            className="btn-outline text-center"
          >
            Add Customer
          </Link>
          <Link
            to="/inventory"
            className="btn-outline text-center"
          >
            Check Inventory
          </Link>
        </div>
      </div>

      {/* Layout for alerts */}
      <div className="grid grid-cols-1 gap-6">
        {/* Low stock alerts */}
        <div className="card p-6">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center">
              <ExclamationTriangleIcon className="h-5 w-5 text-orange-500 mr-2" />
              <h2 className="text-lg font-medium text-gray-900">Low Stock Alerts</h2>
            </div>
            <Link
              to="/inventory"
              className="text-sm text-primary-600 hover:text-primary-700"
            >
              View all
            </Link>
          </div>

          {alertsLoading ? (
            <div className="space-y-3">
              {[...Array(3)].map((_, i) => (
                <div key={i} className="animate-pulse">
                  <div className="h-16 bg-gray-200 rounded-lg"></div>
                </div>
              ))}
            </div>
          ) : lowStockAlerts && lowStockAlerts.length > 0 ? (
            <div className="space-y-3">
              {lowStockAlerts.slice(0, 5).map((alert: any) => (
                <div key={alert.id} className="flex items-center justify-between p-3 bg-orange-50 rounded-lg">
                  <div>
                    <p className="text-sm font-medium text-gray-900">{alert.product_name}</p>
                    <p className="text-sm text-gray-500">
                      Only {alert.current_stock} left in stock
                    </p>
                  </div>
                  <Link
                    to={`/products/${alert.product_id}/edit`}
                    className="text-sm text-orange-600 hover:text-orange-700 font-medium"
                  >
                    Restock
                  </Link>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-8 text-gray-500">
              <ChartBarIcon className="h-12 w-12 mx-auto mb-2 text-gray-300" />
              <p>No low stock alerts</p>
              <p className="text-sm mt-1">All products are well stocked</p>
            </div>
          )}
        </div>
      </div>
      {/* Recent activity */}
      <RecentActivity />
    </div>
  );
};

export default Dashboard;
