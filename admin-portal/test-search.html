<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Unified Search</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .result {
            margin: 10px 0;
            padding: 10px;
            background: #f5f5f5;
            border-radius: 4px;
        }
        .error {
            color: red;
        }
        .success {
            color: green;
        }
    </style>
</head>
<body>
    <h1>Unified Search API Test</h1>
    
    <div class="test-section">
        <h2>Test 1: Search Products</h2>
        <button onclick="testProductSearch()">Test Product Search</button>
        <div id="product-results"></div>
    </div>
    
    <div class="test-section">
        <h2>Test 2: Search Collections</h2>
        <button onclick="testCollectionSearch()">Test Collection Search</button>
        <div id="collection-results"></div>
    </div>
    
    <div class="test-section">
        <h2>Test 3: Unified Search</h2>
        <button onclick="testUnifiedSearch()">Test Unified Search</button>
        <div id="unified-results"></div>
    </div>

    <script>
        const API_BASE = 'http://localhost:8080/api/v1';
        
        async function testProductSearch() {
            const resultsDiv = document.getElementById('product-results');
            resultsDiv.innerHTML = 'Testing...';
            
            try {
                const response = await fetch(`${API_BASE}/products?search=gold&limit=5`);
                const data = await response.json();
                
                resultsDiv.innerHTML = `
                    <div class="success">✓ Products API working</div>
                    <div>Found ${data.total} products</div>
                    <div>Sample: ${data.products[0]?.name || 'No products'}</div>
                `;
            } catch (error) {
                resultsDiv.innerHTML = `<div class="error">✗ Error: ${error.message}</div>`;
            }
        }
        
        async function testCollectionSearch() {
            const resultsDiv = document.getElementById('collection-results');
            resultsDiv.innerHTML = 'Testing...';
            
            try {
                const response = await fetch(`${API_BASE}/collections?search=collection&limit=5`);
                const data = await response.json();
                
                resultsDiv.innerHTML = `
                    <div class="success">✓ Collections API working</div>
                    <div>Found ${data.total} collections</div>
                    <div>Sample: ${data.collections[0]?.name || 'No collections'}</div>
                `;
            } catch (error) {
                resultsDiv.innerHTML = `<div class="error">✗ Error: ${error.message}</div>`;
            }
        }
        
        async function testUnifiedSearch() {
            const resultsDiv = document.getElementById('unified-results');
            resultsDiv.innerHTML = 'Testing...';
            
            try {
                // Test the unified search logic manually
                const [productsResponse, collectionsResponse] = await Promise.all([
                    fetch(`${API_BASE}/products?search=gold&limit=3`),
                    fetch(`${API_BASE}/collections?search=collection&limit=3`)
                ]);
                
                const productsData = await productsResponse.json();
                const collectionsData = await collectionsResponse.json();
                
                const results = [];
                
                // Transform products
                productsData.products.forEach(product => {
                    results.push({
                        type: 'product',
                        title: product.name,
                        subtitle: product.sku,
                        url: `/products/${product.id}/edit`
                    });
                });
                
                // Transform collections
                collectionsData.collections.forEach(collection => {
                    results.push({
                        type: 'collection',
                        title: collection.name,
                        subtitle: collection.slug,
                        url: `/collections/${collection.id}/edit`
                    });
                });
                
                resultsDiv.innerHTML = `
                    <div class="success">✓ Unified search logic working</div>
                    <div>Total results: ${results.length}</div>
                    <div>Results:</div>
                    ${results.map(r => `
                        <div class="result">
                            <strong>${r.type}:</strong> ${r.title} (${r.subtitle})
                        </div>
                    `).join('')}
                `;
            } catch (error) {
                resultsDiv.innerHTML = `<div class="error">✗ Error: ${error.message}</div>`;
            }
        }
    </script>
</body>
</html>
