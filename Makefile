# Anand Jewels - Development Environment Makefile
# This Makefile provides convenient commands to manage the development environment

.PHONY: help start stop restart status logs clean build test install deps

# Default target
help: ## Show this help message
	@echo "Anand Jewels Development Environment"
	@echo "===================================="
	@echo ""
	@echo "Available commands:"
	@echo ""
	@grep -E '^[a-zA-Z_-]+:.*?## .*$$' $(MAKEFILE_LIST) | sort | awk 'BEGIN {FS = ":.*?## "}; {printf "\033[36m%-20s\033[0m %s\n", $$1, $$2}'
	@echo ""
	@echo "Examples:"
	@echo "  make start          # Start all services"
	@echo "  make stop           # Stop all services"
	@echo "  make logs           # View logs from all services"
	@echo "  make status         # Check status of all services"

# Start all services
start: ## Start all services (backend, frontend, admin-portal, databases)
	@echo "🚀 Starting all services..."
	@echo "📦 Starting containers..."
	podman compose up -d
	@echo "⏳ Waiting for services to be ready..."
	@sleep 10
	@echo "🔍 Checking service status..."
	@make status
	@echo ""
	@echo "✅ All services started!"
	@echo ""
	@echo "🌐 Available URLs:"
	@echo "   Frontend:      http://localhost:5173"
	@echo "   Admin Portal:  http://localhost:5173 (same port, different container)"
	@echo "   Backend API:   http://localhost:8080"
	@echo "   MinIO Console: http://localhost:9001"
	@echo "   PostgreSQL:    localhost:5432"
	@echo "   Redis:         localhost:6379"

# Stop all services
stop: ## Stop all services
	@echo "🛑 Stopping all services..."
	podman compose down
	@echo "✅ All services stopped!"

# Restart all services
restart: ## Restart all services
	@echo "🔄 Restarting all services..."
	@make stop
	@sleep 2
	@make start

# Check status of all services
status: ## Check the status of all services
	@echo "📊 Service Status:"
	@echo "=================="
	@podman ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}" | grep jewelry || echo "No services running"
	@echo ""
	@echo "🏥 Health Checks:"
	@echo "=================="
	@echo -n "Backend API:    "
	@curl -s http://localhost:8080/health > /dev/null 2>&1 && echo "✅ Healthy" || echo "❌ Unhealthy"
	@echo -n "Frontend:       "
	@curl -s http://localhost:5173 > /dev/null 2>&1 && echo "✅ Running" || echo "❌ Not responding"
	@echo -n "PostgreSQL:     "
	@podman exec jewelry_postgres pg_isready -U jewelry > /dev/null 2>&1 && echo "✅ Ready" || echo "❌ Not ready"
	@echo -n "Redis:          "
	@podman exec jewelry_redis redis-cli ping > /dev/null 2>&1 && echo "✅ Ready" || echo "❌ Not ready"
	@echo -n "MinIO:          "
	@curl -s http://localhost:9000/minio/health/live > /dev/null 2>&1 && echo "✅ Ready" || echo "❌ Not ready"

# View logs from all services
logs: ## View logs from all services
	@echo "📋 Viewing logs from all services (Ctrl+C to exit)..."
	podman compose logs -f

# View logs from specific service
logs-backend: ## View backend logs
	podman compose logs -f backend

logs-frontend: ## View frontend logs
	podman compose logs -f frontend

logs-admin: ## View admin portal logs
	podman compose logs -f admin-portal

logs-db: ## View database logs
	podman compose logs -f postgres

# Clean up everything (containers, volumes, images)
clean: ## Clean up all containers, volumes, and images
	@echo "🧹 Cleaning up..."
	@echo "⚠️  This will remove all containers, volumes, and images!"
	@read -p "Are you sure? (y/N): " confirm && [ "$$confirm" = "y" ] || exit 1
	podman compose down -v
	podman system prune -f
	@echo "✅ Cleanup complete!"

# Build all services
build: ## Build all Docker images
	@echo "🔨 Building all services..."
	podman compose build --no-cache
	@echo "✅ Build complete!"

# Install dependencies for all projects
install: ## Install dependencies for all projects
	@echo "📦 Installing dependencies..."
	@echo "Installing backend dependencies..."
	cd backend && go mod download
	@echo "Installing frontend dependencies..."
	cd frontend && npm install
	@echo "Installing admin portal dependencies..."
	cd admin-portal && npm install
	@echo "✅ All dependencies installed!"

# Update dependencies
deps: ## Update dependencies for all projects
	@echo "🔄 Updating dependencies..."
	@echo "Updating backend dependencies..."
	cd backend && go mod tidy
	@echo "Updating frontend dependencies..."
	cd frontend && npm update
	@echo "Updating admin portal dependencies..."
	cd admin-portal && npm update
	@echo "✅ All dependencies updated!"

# Run tests
test: ## Run tests for all projects
	@echo "🧪 Running tests..."
	@echo "Testing backend..."
	cd backend && go test ./...
	@echo "Testing frontend..."
	cd frontend && npm test
	@echo "Testing admin portal..."
	cd admin-portal && npm test
	@echo "✅ All tests completed!"

# Development helpers
dev-backend: ## Start only backend services (DB, Redis, MinIO, Backend)
	@echo "🔧 Starting backend development environment..."
	podman compose up -d postgres redis minio backend
	@make status

dev-frontend: ## Start frontend development server locally
	@echo "🎨 Starting frontend development server..."
	cd frontend && npm run dev

dev-admin: ## Start admin portal development server locally
	@echo "⚙️  Starting admin portal development server..."
	cd admin-portal && npm run dev

# Database operations
db-reset: ## Reset the database (WARNING: This will delete all data!)
	@echo "🗄️  Resetting database..."
	@echo "⚠️  This will delete ALL data!"
	@read -p "Are you sure? (y/N): " confirm && [ "$$confirm" = "y" ] || exit 1
	podman compose stop postgres
	podman volume rm jewelry_postgres_data || true
	podman compose up -d postgres
	@echo "✅ Database reset complete!"

db-backup: ## Create a database backup
	@echo "💾 Creating database backup..."
	@mkdir -p backups
	podman exec jewelry_postgres pg_dump -U jewelry jewelry > backups/backup_$(shell date +%Y%m%d_%H%M%S).sql
	@echo "✅ Backup created in backups/ directory"

db-restore: ## Restore database from backup (specify BACKUP_FILE=filename)
	@echo "📥 Restoring database from backup..."
	@if [ -z "$(BACKUP_FILE)" ]; then echo "❌ Please specify BACKUP_FILE=filename"; exit 1; fi
	@if [ ! -f "$(BACKUP_FILE)" ]; then echo "❌ Backup file not found: $(BACKUP_FILE)"; exit 1; fi
	podman exec -i jewelry_postgres psql -U jewelry jewelry < $(BACKUP_FILE)
	@echo "✅ Database restored from $(BACKUP_FILE)"

# Monitoring and debugging
monitor: ## Monitor resource usage of all containers
	@echo "📊 Monitoring container resource usage (Ctrl+C to exit)..."
	watch -n 2 'podman stats --no-stream'

shell-backend: ## Open shell in backend container
	podman exec -it jewelry_backend sh

shell-db: ## Open PostgreSQL shell
	podman exec -it jewelry_postgres psql -U jewelry jewelry

shell-redis: ## Open Redis CLI
	podman exec -it jewelry_redis redis-cli

# Quick development workflow
quick-start: ## Quick start for development (backend + one frontend)
	@echo "⚡ Quick start for development..."
	@echo "Starting backend services..."
	@make dev-backend
	@echo ""
	@echo "Choose which frontend to start:"
	@echo "1) Customer Frontend (port 5173)"
	@echo "2) Admin Portal (port 5173)"
	@read -p "Enter choice (1 or 2): " choice; \
	if [ "$$choice" = "1" ]; then \
		echo "Starting customer frontend..."; \
		make dev-frontend; \
	elif [ "$$choice" = "2" ]; then \
		echo "Starting admin portal..."; \
		make dev-admin; \
	else \
		echo "Invalid choice. Please run 'make dev-frontend' or 'make dev-admin' manually."; \
	fi

# Environment info
info: ## Show environment information
	@echo "🔍 Environment Information:"
	@echo "=========================="
	@echo "Podman version: $(shell podman --version)"
	@echo "Node.js version: $(shell node --version 2>/dev/null || echo 'Not installed')"
	@echo "Go version: $(shell go version 2>/dev/null || echo 'Not installed')"
	@echo "Current directory: $(shell pwd)"
	@echo ""
	@echo "📁 Project structure:"
	@ls -la | grep -E '^d.*' | awk '{print "  " $$9}' | grep -v '^\.$\|^\.\.$'
