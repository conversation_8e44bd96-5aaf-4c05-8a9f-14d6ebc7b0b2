# Anand Jewels - Development Environment Makefile
# This Makefile provides convenient commands to manage the development environment

.PHONY: help start stop restart status logs clean build test install deps

# Default target
help: ## Show this help message
	@echo "Anand Jewels Development Environment"
	@echo "===================================="
	@echo ""
	@echo "Available commands:"
	@echo ""
	@grep -E '^[a-zA-Z_-]+:.*?## .*$$' $(MAKEFILE_LIST) | sort | awk 'BEGIN {FS = ":.*?## "}; {printf "\033[36m%-20s\033[0m %s\n", $$1, $$2}'
	@echo ""
	@echo "Examples:"
	@echo "  make start          # Start all services"
	@echo "  make stop           # Stop all services"
	@echo "  make logs           # View logs from all services"
	@echo "  make status         # Check status of all services"

# Start all services
start: ## Start all services (backend, frontend, admin-portal, databases)
	@echo "🚀 Starting all services..."
	@echo "📦 Starting containers..."
	podman compose up -d
	@echo "⏳ Waiting for services to be ready..."
	@sleep 10
	@echo "🔍 Checking service status..."
	@make status
	@echo ""
	@echo "✅ All services started!"
	@echo ""
	@echo "🌐 Available URLs:"
	@echo "   Frontend:      http://localhost:5173"
	@echo "   Admin Portal:  http://localhost:5173 (same port, different container)"
	@echo "   Backend API:   http://localhost:8080"
	@echo "   MinIO Console: http://localhost:9001"
	@echo "   PostgreSQL:    localhost:5432"
	@echo "   Redis:         localhost:6379"

# Stop all services
stop: ## Stop all services
	@echo "🛑 Stopping all services..."
	podman compose down
	@echo "✅ All services stopped!"

# Restart all services
restart: ## Restart all services
	@echo "🔄 Restarting all services..."
	@make stop
	@sleep 2
	@make start

# Check status of all services
status: ## Check the status of all services
	@echo "📊 Service Status:"
	@echo "=================="
	@podman ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}" | grep jewelry || echo "No services running"
	@echo ""
	@echo "🏥 Health Checks:"
	@echo "=================="
	@echo -n "Backend API:    "
	@curl -s http://localhost:8080/health > /dev/null 2>&1 && echo "✅ Healthy" || echo "❌ Unhealthy"
	@echo -n "Frontend:       "
	@curl -s http://localhost:5173 > /dev/null 2>&1 && echo "✅ Running" || echo "❌ Not responding"
	@echo -n "PostgreSQL:     "
	@podman exec jewelry_postgres pg_isready -U jewelry > /dev/null 2>&1 && echo "✅ Ready" || echo "❌ Not ready"
	@echo -n "Redis:          "
	@podman exec jewelry_redis redis-cli ping > /dev/null 2>&1 && echo "✅ Ready" || echo "❌ Not ready"
	@echo -n "MinIO:          "
	@curl -s http://localhost:9000/minio/health/live > /dev/null 2>&1 && echo "✅ Ready" || echo "❌ Not ready"

# View logs from all services
logs: ## View logs from all services
	@echo "📋 Viewing logs from all services (Ctrl+C to exit)..."
	podman compose logs -f

# View logs from specific service
logs-backend: ## View backend logs
	podman compose logs -f backend

logs-frontend: ## View frontend logs
	podman compose logs -f frontend

logs-admin: ## View admin portal logs
	podman compose logs -f admin-portal

logs-db: ## View database logs
	podman compose logs -f postgres

# Clean up everything (containers, volumes, images)
clean: ## Clean up all containers, volumes, and images
	@echo "🧹 Cleaning up..."
	@echo "⚠️  This will remove all containers, volumes, and images!"
	@read -p "Are you sure? (y/N): " confirm && [ "$$confirm" = "y" ] || exit 1
	podman compose down -v
	podman system prune -f
	@echo "✅ Cleanup complete!"

# Build all services
build: ## Build all Docker images
	@echo "🔨 Building all services..."
	podman compose build --no-cache
	@echo "✅ Build complete!"

# Install dependencies for all projects
install: ## Install dependencies for all projects
	@echo "📦 Installing dependencies..."
	@echo "Installing backend dependencies..."
	cd backend && go mod download
	@echo "Installing frontend dependencies..."
	cd frontend && npm install
	@echo "Installing admin portal dependencies..."
	cd admin-portal && npm install
	@echo "✅ All dependencies installed!"

# Update dependencies
deps: ## Update dependencies for all projects
	@echo "🔄 Updating dependencies..."
	@echo "Updating backend dependencies..."
	cd backend && go mod tidy
	@echo "Updating frontend dependencies..."
	cd frontend && npm update
	@echo "Updating admin portal dependencies..."
	cd admin-portal && npm update
	@echo "✅ All dependencies updated!"

# Run tests
test: ## Run tests for all projects
	@echo "🧪 Running tests..."
	@echo "Testing backend..."
	cd backend && go test ./...
	@echo "Testing frontend..."
	cd frontend && npm test
	@echo "Testing admin portal..."
	cd admin-portal && npm test
	@echo "✅ All tests completed!"

# Development helpers
dev-backend: ## Start only backend services (DB, Redis, MinIO, Backend)
	@echo "🔧 Starting backend development environment..."
	podman compose up -d postgres redis minio backend
	@make status

dev-frontend: ## Start frontend development server locally
	@echo "🎨 Starting frontend development server..."
	cd frontend && npm run dev

dev-admin: ## Start admin portal development server locally
	@echo "⚙️  Starting admin portal development server..."
	cd admin-portal && npm run dev

# Database operations
db-reset: ## Reset the database (WARNING: This will delete all data!)
	@echo "🗄️  Resetting database..."
	@echo "⚠️  This will delete ALL data!"
	@read -p "Are you sure? (y/N): " confirm && [ "$$confirm" = "y" ] || exit 1
	podman compose stop postgres
	podman volume rm jewelry_postgres_data || true
	podman compose up -d postgres
	@echo "✅ Database reset complete!"

db-backup: ## Create a database backup
	@echo "💾 Creating database backup..."
	@mkdir -p backups
	podman exec jewelry_postgres pg_dump -U jewelry jewelry > backups/backup_$(shell date +%Y%m%d_%H%M%S).sql
	@echo "✅ Backup created in backups/ directory"

db-restore: ## Restore database from backup (specify BACKUP_FILE=filename)
	@echo "📥 Restoring database from backup..."
	@if [ -z "$(BACKUP_FILE)" ]; then echo "❌ Please specify BACKUP_FILE=filename"; exit 1; fi
	@if [ ! -f "$(BACKUP_FILE)" ]; then echo "❌ Backup file not found: $(BACKUP_FILE)"; exit 1; fi
	podman exec -i jewelry_postgres psql -U jewelry jewelry < $(BACKUP_FILE)
	@echo "✅ Database restored from $(BACKUP_FILE)"

# Monitoring and debugging
monitor: ## Monitor resource usage of all containers
	@echo "📊 Monitoring container resource usage (Ctrl+C to exit)..."
	watch -n 2 'podman stats --no-stream'

shell-backend: ## Open shell in backend container
	podman exec -it jewelry_backend sh

shell-db: ## Open PostgreSQL shell
	podman exec -it jewelry_postgres psql -U jewelry jewelry

shell-redis: ## Open Redis CLI
	podman exec -it jewelry_redis redis-cli

# Quick development workflow
quick-start: ## Quick start for development (backend + one frontend)
	@echo "⚡ Quick start for development..."
	@echo "Starting backend services..."
	@make dev-backend
	@echo ""
	@echo "Choose which frontend to start:"
	@echo "1) Customer Frontend (port 5173)"
	@echo "2) Admin Portal (port 5173)"
	@read -p "Enter choice (1 or 2): " choice; \
	if [ "$$choice" = "1" ]; then \
		echo "Starting customer frontend..."; \
		make dev-frontend; \
	elif [ "$$choice" = "2" ]; then \
		echo "Starting admin portal..."; \
		make dev-admin; \
	else \
		echo "Invalid choice. Please run 'make dev-frontend' or 'make dev-admin' manually."; \
	fi

# Railway deployment commands
railway-build-backend: ## Build backend for Railway deployment
	@echo "🚀 Building backend for Railway..."
	cd backend && go mod tidy && go build -o bin/main ./cmd/main.go
	@echo "✅ Backend build complete!"

railway-start-backend: ## Start backend for Railway deployment
	@echo "🚀 Starting backend for Railway..."
	cd backend && ./bin/main

railway-build-frontend: ## Build frontend for Railway deployment
	@echo "🎨 Building frontend for Railway..."
	cd frontend && npm ci && npm run build
	@echo "✅ Frontend build complete!"

railway-start-frontend: ## Start frontend for Railway deployment
	@echo "🎨 Starting frontend for Railway..."
	cd frontend && npm run preview

railway-build-admin: ## Build admin portal for Railway deployment
	@echo "⚙️  Building admin portal for Railway..."
	cd admin-portal && npm ci && npm run build
	@echo "✅ Admin portal build complete!"

railway-start-admin: ## Start admin portal for Railway deployment
	@echo "⚙️  Starting admin portal for Railway..."
	cd admin-portal && npm run preview

# Combined Railway commands (for Railway's build and start commands)
railway-build: ## Build all services for Railway (use this as Railway build command)
	@echo "🚀 Building all services for Railway deployment..."
	@make railway-build-backend
	@make railway-build-frontend
	@make railway-build-admin
	@echo "✅ All services built for Railway!"

railway-start: ## Start service for Railway (use this as Railway start command with SERVICE env var)
	@echo "🚀 Starting service for Railway..."
	@if [ "$(SERVICE)" = "backend" ]; then \
		make railway-start-backend; \
	elif [ "$(SERVICE)" = "frontend" ]; then \
		make railway-start-frontend; \
	elif [ "$(SERVICE)" = "admin" ]; then \
		make railway-start-admin; \
	else \
		echo "❌ Please set SERVICE environment variable to: backend, frontend, or admin"; \
		exit 1; \
	fi

# Docker commands for Kubernetes-ready images
docker-build-images: ## Build all Docker images locally
	@echo "🐳 Building Docker images locally..."
	@echo "Building backend image..."
	cd backend && docker build -t anand-jewels/backend:latest .
	@echo "Building frontend image..."
	cd frontend && docker build -t anand-jewels/frontend:latest .
	@echo "Building admin portal image..."
	cd admin-portal && docker build -t anand-jewels/admin-portal:latest .
	@echo "✅ All Docker images built!"

docker-test-local: ## Test Kubernetes-ready images locally with Docker Compose
	@echo "🧪 Testing Kubernetes-ready images locally..."
	docker-compose -f docker-compose.k8s.yml up -d
	@echo "✅ Services started! Access:"
	@echo "   Frontend:      http://localhost:3000"
	@echo "   Admin Portal:  http://localhost:3001"
	@echo "   Backend API:   http://localhost:8080"
	@echo "   MinIO Console: http://localhost:9001"

docker-test-stop: ## Stop local Docker Compose test
	@echo "🛑 Stopping local Docker Compose test..."
	docker-compose -f docker-compose.k8s.yml down
	@echo "✅ Test environment stopped!"

docker-test-clean: ## Clean up local Docker Compose test
	@echo "🧹 Cleaning up local Docker Compose test..."
	docker-compose -f docker-compose.k8s.yml down -v
	@echo "✅ Test environment cleaned!"

# Kubernetes deployment commands
k8s-build-images: ## Build all Docker images for Kubernetes
	@echo "🐳 Building Docker images for Kubernetes..."
	@echo "Building backend image..."
	cd backend && docker build -t anand-jewels/backend:latest .
	@echo "Building frontend image..."
	cd frontend && docker build -t anand-jewels/frontend:latest .
	@echo "Building admin portal image..."
	cd admin-portal && docker build -t anand-jewels/admin-portal:latest .
	@echo "✅ All Docker images built!"

k8s-push-images: ## Push Docker images to registry (set REGISTRY variable)
	@echo "📤 Pushing Docker images to registry..."
	@if [ -z "$(REGISTRY)" ]; then echo "❌ Please set REGISTRY variable (e.g., REGISTRY=your-registry.com make k8s-push-images)"; exit 1; fi
	docker tag anand-jewels/backend:latest $(REGISTRY)/anand-jewels/backend:latest
	docker tag anand-jewels/frontend:latest $(REGISTRY)/anand-jewels/frontend:latest
	docker tag anand-jewels/admin-portal:latest $(REGISTRY)/anand-jewels/admin-portal:latest
	docker push $(REGISTRY)/anand-jewels/backend:latest
	docker push $(REGISTRY)/anand-jewels/frontend:latest
	docker push $(REGISTRY)/anand-jewels/admin-portal:latest
	@echo "✅ All images pushed to $(REGISTRY)!"

k8s-deploy-namespace: ## Create Kubernetes namespace
	@echo "🏗️  Creating Kubernetes namespace..."
	kubectl apply -f k8s/namespace.yaml
	@echo "✅ Namespace created!"

k8s-deploy-secrets: ## Deploy secrets to Kubernetes
	@echo "🔐 Deploying secrets..."
	@echo "⚠️  Make sure to update k8s/secrets.yaml with your actual base64-encoded values!"
	kubectl apply -f k8s/secrets.yaml
	@echo "✅ Secrets deployed!"

k8s-deploy-config: ## Deploy ConfigMaps to Kubernetes
	@echo "⚙️  Deploying ConfigMaps..."
	kubectl apply -f k8s/configmap.yaml
	@echo "✅ ConfigMaps deployed!"

k8s-deploy-storage: ## Deploy storage services (PostgreSQL, Redis, MinIO)
	@echo "💾 Deploying storage services..."
	kubectl apply -f k8s/postgres.yaml
	kubectl apply -f k8s/redis.yaml
	kubectl apply -f k8s/minio.yaml
	@echo "✅ Storage services deployed!"

k8s-deploy-backend: ## Deploy backend service
	@echo "🚀 Deploying backend service..."
	kubectl apply -f k8s/backend.yaml
	@echo "✅ Backend service deployed!"

k8s-deploy-frontend: ## Deploy frontend services
	@echo "🎨 Deploying frontend services..."
	kubectl apply -f k8s/frontend.yaml
	kubectl apply -f k8s/admin-portal.yaml
	@echo "✅ Frontend services deployed!"

k8s-deploy-ingress: ## Deploy ingress configuration
	@echo "🌐 Deploying ingress..."
	kubectl apply -f k8s/ingress.yaml
	@echo "✅ Ingress deployed!"

k8s-deploy-all: ## Deploy everything to Kubernetes
	@echo "🚀 Deploying complete application to Kubernetes..."
	@make k8s-deploy-namespace
	@make k8s-deploy-secrets
	@make k8s-deploy-config
	@make k8s-deploy-storage
	@echo "⏳ Waiting for storage services to be ready..."
	@sleep 30
	@make k8s-deploy-backend
	@echo "⏳ Waiting for backend to be ready..."
	@sleep 15
	@make k8s-deploy-frontend
	@make k8s-deploy-ingress
	@echo "✅ Complete application deployed!"

k8s-status: ## Check Kubernetes deployment status
	@echo "📊 Kubernetes Deployment Status:"
	@echo "================================"
	@echo "Namespace:"
	@kubectl get namespace jewelry-store 2>/dev/null || echo "❌ Namespace not found"
	@echo ""
	@echo "Pods:"
	@kubectl get pods -n jewelry-store 2>/dev/null || echo "❌ No pods found"
	@echo ""
	@echo "Services:"
	@kubectl get services -n jewelry-store 2>/dev/null || echo "❌ No services found"
	@echo ""
	@echo "Ingress:"
	@kubectl get ingress -n jewelry-store 2>/dev/null || echo "❌ No ingress found"

k8s-logs-backend: ## View backend logs
	kubectl logs -f deployment/backend -n jewelry-store

k8s-logs-frontend: ## View frontend logs
	kubectl logs -f deployment/frontend -n jewelry-store

k8s-logs-admin: ## View admin portal logs
	kubectl logs -f deployment/admin-portal -n jewelry-store

k8s-shell-backend: ## Open shell in backend pod
	kubectl exec -it deployment/backend -n jewelry-store -- sh

k8s-shell-postgres: ## Open PostgreSQL shell
	kubectl exec -it deployment/postgres -n jewelry-store -- psql -U jewelry jewelry

k8s-shell-redis: ## Open Redis CLI
	kubectl exec -it deployment/redis -n jewelry-store -- redis-cli

k8s-port-forward-backend: ## Port forward backend service (localhost:8080)
	kubectl port-forward service/backend-service 8080:8080 -n jewelry-store

k8s-port-forward-frontend: ## Port forward frontend service (localhost:3000)
	kubectl port-forward service/frontend-service 3000:80 -n jewelry-store

k8s-port-forward-admin: ## Port forward admin portal service (localhost:3001)
	kubectl port-forward service/admin-portal-service 3001:80 -n jewelry-store

k8s-port-forward-minio: ## Port forward MinIO console (localhost:9001)
	kubectl port-forward service/minio-service 9001:9001 -n jewelry-store

k8s-clean: ## Clean up Kubernetes deployment
	@echo "🧹 Cleaning up Kubernetes deployment..."
	@echo "⚠️  This will delete all resources in the jewelry-store namespace!"
	@read -p "Are you sure? (y/N): " confirm && [ "$$confirm" = "y" ] || exit 1
	kubectl delete namespace jewelry-store
	@echo "✅ Cleanup complete!"

k8s-restart-backend: ## Restart backend deployment
	kubectl rollout restart deployment/backend -n jewelry-store

k8s-restart-frontend: ## Restart frontend deployment
	kubectl rollout restart deployment/frontend -n jewelry-store

k8s-restart-admin: ## Restart admin portal deployment
	kubectl rollout restart deployment/admin-portal -n jewelry-store

k8s-scale-backend: ## Scale backend deployment (set REPLICAS variable)
	@if [ -z "$(REPLICAS)" ]; then echo "❌ Please set REPLICAS variable (e.g., REPLICAS=3 make k8s-scale-backend)"; exit 1; fi
	kubectl scale deployment/backend --replicas=$(REPLICAS) -n jewelry-store

k8s-scale-frontend: ## Scale frontend deployment (set REPLICAS variable)
	@if [ -z "$(REPLICAS)" ]; then echo "❌ Please set REPLICAS variable (e.g., REPLICAS=3 make k8s-scale-frontend)"; exit 1; fi
	kubectl scale deployment/frontend --replicas=$(REPLICAS) -n jewelry-store

k8s-scale-admin: ## Scale admin portal deployment (set REPLICAS variable)
	@if [ -z "$(REPLICAS)" ]; then echo "❌ Please set REPLICAS variable (e.g., REPLICAS=3 make k8s-scale-admin)"; exit 1; fi
	kubectl scale deployment/admin-portal --replicas=$(REPLICAS) -n jewelry-store

# Environment info
info: ## Show environment information
	@echo "🔍 Environment Information:"
	@echo "=========================="
	@echo "Podman version: $(shell podman --version 2>/dev/null || echo 'Not installed')"
	@echo "Docker version: $(shell docker --version 2>/dev/null || echo 'Not installed')"
	@echo "Kubernetes version: $(shell kubectl version --client --short 2>/dev/null || echo 'Not installed')"
	@echo "Node.js version: $(shell node --version 2>/dev/null || echo 'Not installed')"
	@echo "Go version: $(shell go version 2>/dev/null || echo 'Not installed')"
	@echo "Current directory: $(shell pwd)"
	@echo ""
	@echo "📁 Project structure:"
	@ls -la | grep -E '^d.*' | awk '{print "  " $$9}' | grep -v '^\.$\|^\.\.$'
