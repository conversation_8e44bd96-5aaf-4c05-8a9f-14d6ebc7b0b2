import React, { useState, useEffect } from 'react';
import { use<PERSON>ara<PERSON>, Link } from 'react-router-dom';
import { useQuery } from '@tanstack/react-query';
import {
  ShareIcon,
  EyeIcon,
  CalendarIcon,
  ArrowLeftIcon,
  MagnifyingGlassIcon
} from '@heroicons/react/24/outline';
import { getCollection, getCollectionWithProducts, trackCollectionView } from '../lib/api';
import { useNotification } from '../contexts/NotificationContext';
import ProductGrid from '../components/products/ProductGrid';
import ProductFilters from '../components/products/ProductFilters';
import SearchBar from '../components/common/SearchBar';
import type { FilterOptions } from '../types';

const CollectionPage: React.FC = () => {
  const { slug } = useParams<{ slug: string }>();
  const { addNotification } = useNotification();
  const [filters, setFilters] = useState<FilterOptions>({
    search: '',
    page: 1,
    limit: 20,
  });
  const [showFilters, setShowFilters] = useState(false);
  const [filteredProducts, setFilteredProducts] = useState<any[]>([]);

  // Get collection basic info
  const { data: collection, isLoading: collectionLoading, error: collectionError } = useQuery({
    queryKey: ['collection', slug],
    queryFn: () => getCollection(slug!),
    enabled: !!slug,
  });

  // Get collection with products
  const { data: collectionWithProducts, isLoading: productsLoading } = useQuery({
    queryKey: ['collection-products', collection?.id],
    queryFn: () => getCollectionWithProducts(collection!.id),
    enabled: !!collection?.id,
  });

  // Track collection view
  useEffect(() => {
    if (collection?.id) {
      trackCollectionView(collection.id).catch(console.error);
    }
  }, [collection?.id]);

  // Filter products based on current filters
  useEffect(() => {
    if (!collectionWithProducts?.products) {
      setFilteredProducts([]);
      return;
    }

    let filtered = [...collectionWithProducts.products];

    // Search filter
    if (filters.search) {
      const searchTerm = filters.search.toLowerCase();
      filtered = filtered.filter(product =>
        product.name.toLowerCase().includes(searchTerm) ||
        product.description?.toLowerCase().includes(searchTerm) ||
        product.material?.toLowerCase().includes(searchTerm) ||
        product.gemstone?.toLowerCase().includes(searchTerm) ||
        product.tags.some((tag: string) => tag.toLowerCase().includes(searchTerm))
      );
    }

    // Category filter
    if (filters.category) {
      filtered = filtered.filter(product => product.category === filters.category);
    }

    // Material filter
    if (filters.material) {
      filtered = filtered.filter(product =>
        product.material?.toLowerCase().includes(filters.material!.toLowerCase())
      );
    }

    // Price range filter
    if (filters.price_min !== undefined) {
      filtered = filtered.filter(product => product.price >= filters.price_min!);
    }
    if (filters.price_max !== undefined) {
      filtered = filtered.filter(product => product.price <= filters.price_max!);
    }

    // Availability filter
    if (filters.availability) {
      filtered = filtered.filter(product => product.availability === filters.availability);
    }

    // Sorting
    if (filters.sort_by) {
      filtered.sort((a, b) => {
        let aValue, bValue;

        switch (filters.sort_by) {
          case 'name':
            aValue = a.name.toLowerCase();
            bValue = b.name.toLowerCase();
            break;
          case 'price':
            aValue = a.price;
            bValue = b.price;
            break;
          case 'created_at':
            aValue = new Date(a.created_at).getTime();
            bValue = new Date(b.created_at).getTime();
            break;
          default:
            return 0;
        }

        if (aValue < bValue) return filters.sort_order === 'asc' ? -1 : 1;
        if (aValue > bValue) return filters.sort_order === 'asc' ? 1 : -1;
        return 0;
      });
    }

    setFilteredProducts(filtered);
  }, [collectionWithProducts?.products, filters]);

  const handleSearch = (query: string) => {
    setFilters(prev => ({ ...prev, search: query, page: 1 }));
  };

  const handleFilterChange = (newFilters: Partial<FilterOptions>) => {
    setFilters(prev => ({ ...prev, ...newFilters, page: 1 }));
  };

  const handleClearFilters = () => {
    setFilters({
      search: '',
      page: 1,
      limit: 20,
    });
  };

  const handleShare = async () => {
    try {
      await navigator.share({
        title: collection?.name,
        text: collection?.description || `Check out this jewelry collection: ${collection?.name}`,
        url: window.location.href,
      });
    } catch (error) {
      // Fallback to copying URL
      await navigator.clipboard.writeText(window.location.href);
      addNotification({
        type: 'success',
        message: 'Collection link copied to clipboard!'
      });
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-IN', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  if (collectionError) {
    return (
      <div className="container-custom py-16">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">Collection Not Found</h1>
          <p className="text-gray-600 mb-8">
            The collection you're looking for doesn't exist or is no longer available.
          </p>
          <Link to="/" className="btn-primary">
            Go Home
          </Link>
        </div>
      </div>
    );
  }

  if (collectionLoading) {
    return (
      <div className="container-custom py-8">
        <div className="animate-pulse">
          <div className="skeleton h-8 w-64 mb-4"></div>
          <div className="skeleton h-4 w-96 mb-8"></div>
          <div className="skeleton h-64 w-full mb-8"></div>
          <div className="product-grid">
            {Array.from({ length: 8 }).map((_, index) => (
              <div key={index} className="card">
                <div className="skeleton-image"></div>
                <div className="p-4 space-y-2">
                  <div className="skeleton-text"></div>
                  <div className="skeleton-text w-1/2"></div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Breadcrumb */}
      <div className="bg-white border-b border-gray-200">
        <div className="container-custom py-4">
          <nav className="flex items-center space-x-2 text-sm">
            <Link to="/" className="text-gray-500 hover:text-gray-700">
              Home
            </Link>
            <span className="text-gray-400">/</span>
            <Link to="/collections" className="text-gray-500 hover:text-gray-700">
              Collections
            </Link>
            <span className="text-gray-400">/</span>
            <span className="text-gray-900 font-medium">{collection?.name}</span>
          </nav>
        </div>
      </div>

      <div className="container-custom py-8">
        {/* Back Button */}
        <div className="mb-6">
          <Link
            to="/"
            className="inline-flex items-center text-gray-600 hover:text-gray-900 transition-colors"
          >
            <ArrowLeftIcon className="h-4 w-4 mr-2" />
            Back to Home
          </Link>
        </div>

        {/* Collection Header */}
        <div className="mb-8">
          {collection?.cover_image_url && (
            <div className="aspect-video lg:aspect-[3/1] overflow-hidden rounded-lg mb-6">
              <img
                src={collection.cover_image_url}
                alt={collection.name}
                className="w-full h-full object-cover"
              />
            </div>
          )}

          <div className="flex flex-col lg:flex-row lg:items-start lg:justify-between gap-6">
            <div className="flex-1">
              <h1 className="text-3xl lg:text-4xl font-bold font-serif text-gray-900 mb-4">
                {collection?.name}
              </h1>

              {collection?.description && (
                <p className="text-lg text-gray-600 mb-6 max-w-3xl">
                  {collection.description}
                </p>
              )}

              {/* Collection Stats */}
              <div className="flex flex-wrap items-center gap-6 text-sm text-gray-500">
                <div className="flex items-center space-x-2">
                  <EyeIcon className="h-4 w-4" />
                  <span>{collection?.view_count} views</span>
                </div>

                <div className="flex items-center space-x-2">
                  <CalendarIcon className="h-4 w-4" />
                  <span>Created {collection?.created_at && formatDate(collection.created_at)}</span>
                </div>

                <div className="flex items-center space-x-2">
                  <span className="font-medium">
                    {filteredProducts.length} {filteredProducts.length === 1 ? 'item' : 'items'}
                  </span>
                </div>

                {collection?.expires_at && (
                  <div className="flex items-center space-x-2 text-amber-600">
                    <CalendarIcon className="h-4 w-4" />
                    <span>Expires {formatDate(collection.expires_at)}</span>
                  </div>
                )}
              </div>
            </div>

            {/* Share Button */}
            <div className="flex-shrink-0">
              <button
                onClick={handleShare}
                className="btn-outline flex items-center space-x-2"
              >
                <ShareIcon className="h-4 w-4" />
                <span>Share Collection</span>
              </button>
            </div>
          </div>
        </div>

        {/* Search and Filters */}
        <div className="mb-8 space-y-4">
          {/* Search Bar */}
          <div className="flex flex-col lg:flex-row gap-4">
            <div className="flex-1 max-w-md">
              <SearchBar
                onSearch={handleSearch}
                placeholder="Search in this collection..."
              />
            </div>

            <div className="flex items-center space-x-4">
              <span className="text-sm text-gray-600">
                Showing {filteredProducts.length} of {collectionWithProducts?.products?.length || 0} items
              </span>
            </div>
          </div>

          {/* Filters */}
          <ProductFilters
            filters={filters}
            onFiltersChange={handleFilterChange}
            onClearFilters={handleClearFilters}
            isOpen={showFilters}
            onToggle={() => setShowFilters(!showFilters)}
          />
        </div>

        {/* Products Grid */}
        <ProductGrid
          products={filteredProducts}
          isLoading={productsLoading}
          emptyMessage={
            filters.search || filters.category || filters.material || filters.price_min || filters.price_max
              ? "No products match your current filters. Try adjusting your search criteria."
              : "This collection doesn't have any products yet."
          }
        />

        {/* Load More Button (if needed for pagination) */}
        {filteredProducts.length > 0 && filteredProducts.length < (collectionWithProducts?.products?.length || 0) && (
          <div className="text-center mt-12">
            <button className="btn-outline btn-lg">
              Load More Products
            </button>
          </div>
        )}
      </div>
    </div>
  );
};

export default CollectionPage;
