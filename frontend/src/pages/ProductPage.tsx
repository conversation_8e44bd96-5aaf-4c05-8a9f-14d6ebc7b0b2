import React, { useState, useEffect } from 'react';
import { use<PERSON><PERSON><PERSON>, <PERSON>, useNavigate } from 'react-router-dom';
import { useQuery } from '@tanstack/react-query';
import {
  HeartIcon,
  ShoppingBagIcon,
  ShareIcon,
  ArrowLeftIcon,
  CheckCircleIcon,
  XCircleIcon,
  InformationCircleIcon,
  ScaleIcon,
  SparklesIcon
} from '@heroicons/react/24/outline';
import { HeartIcon as HeartSolidIcon } from '@heroicons/react/24/solid';
import { getProduct, trackProductView } from '../lib/api';
import { useCart } from '../contexts/CartContext';
import { useWishlist } from '../contexts/WishlistContext';
import { useNotification } from '../contexts/NotificationContext';
import ProductImageGallery from '../components/products/ProductImageGallery';
import ProductGrid from '../components/products/ProductGrid';

const ProductPage: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { addItem: addToCart, isInCart, getItemQuantity } = useCart();
  const { toggleItem: toggleWishlist, isInWishlist } = useWishlist();
  const { addNotification } = useNotification();
  const [quantity, setQuantity] = useState(1);
  const [selectedTab, setSelectedTab] = useState<'description' | 'specifications' | 'care'>('description');

  // Get product details
  const { data: product, isLoading, error } = useQuery({
    queryKey: ['product', id],
    queryFn: () => getProduct(id!),
    enabled: !!id,
  });

  // Track product view
  useEffect(() => {
    if (product?.id) {
      trackProductView(product.id).catch(console.error);
    }
  }, [product?.id]);

  const handleAddToCart = () => {
    if (!product) return;

    if (product.availability !== 'available') {
      addNotification({
        type: 'warning',
        message: 'This item is currently out of stock'
      });
      return;
    }

    if (product.stock_quantity < quantity) {
      addNotification({
        type: 'warning',
        message: `Only ${product.stock_quantity} items available in stock`
      });
      return;
    }

    addToCart(product, quantity);
    addNotification({
      type: 'success',
      message: `${product.name} added to cart`
    });
  };

  const handleToggleWishlist = () => {
    if (!product) return;

    const wasInWishlist = isInWishlist(product.id);
    toggleWishlist(product);

    addNotification({
      type: 'success',
      message: wasInWishlist
        ? `${product.name} removed from wishlist`
        : `${product.name} added to wishlist`
    });
  };

  const handleShare = async () => {
    if (!product) return;

    try {
      await navigator.share({
        title: product.name,
        text: product.description || `Check out this beautiful jewelry piece: ${product.name}`,
        url: window.location.href,
      });
    } catch (error) {
      // Fallback to copying URL
      await navigator.clipboard.writeText(window.location.href);
      addNotification({
        type: 'success',
        message: 'Product link copied to clipboard!'
      });
    }
  };

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      minimumFractionDigits: 0,
    }).format(price);
  };

  const getAvailabilityInfo = () => {
    if (!product) return null;

    switch (product.availability) {
      case 'available':
        return {
          icon: <CheckCircleIcon className="h-5 w-5 text-green-500" />,
          text: 'In Stock',
          color: 'text-green-600',
          bgColor: 'bg-green-50'
        };
      case 'out_of_stock':
        return {
          icon: <XCircleIcon className="h-5 w-5 text-red-500" />,
          text: 'Out of Stock',
          color: 'text-red-600',
          bgColor: 'bg-red-50'
        };
      case 'discontinued':
        return {
          icon: <InformationCircleIcon className="h-5 w-5 text-yellow-500" />,
          text: 'Discontinued',
          color: 'text-yellow-600',
          bgColor: 'bg-yellow-50'
        };
      default:
        return null;
    }
  };

  if (error) {
    return (
      <div className="container-custom py-16">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">Product Not Found</h1>
          <p className="text-gray-600 mb-8">
            The product you're looking for doesn't exist or is no longer available.
          </p>
          <Link to="/" className="btn-primary">
            Go Home
          </Link>
        </div>
      </div>
    );
  }

  if (isLoading) {
    return (
      <div className="container-custom py-8">
        <div className="animate-pulse">
          <div className="skeleton h-6 w-32 mb-6"></div>
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <div className="skeleton-image aspect-square"></div>
            <div className="space-y-4">
              <div className="skeleton h-8 w-3/4"></div>
              <div className="skeleton h-6 w-1/2"></div>
              <div className="skeleton h-4 w-full"></div>
              <div className="skeleton h-4 w-2/3"></div>
              <div className="skeleton h-12 w-full"></div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  const availabilityInfo = getAvailabilityInfo();
  const currentQuantityInCart = getItemQuantity(product?.id || '');

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Breadcrumb */}
      <div className="bg-white border-b border-gray-200">
        <div className="container-custom py-4">
          <nav className="flex items-center space-x-2 text-sm">
            <Link to="/" className="text-gray-500 hover:text-gray-700">
              Home
            </Link>
            <span className="text-gray-400">/</span>
            <Link to="/products" className="text-gray-500 hover:text-gray-700">
              Products
            </Link>
            <span className="text-gray-400">/</span>
            <span className="text-gray-900 font-medium">{product?.name}</span>
          </nav>
        </div>
      </div>

      <div className="container-custom py-8">
        {/* Back Button */}
        <div className="mb-6">
          <button
            onClick={() => navigate(-1)}
            className="inline-flex items-center text-gray-600 hover:text-gray-900 transition-colors"
          >
            <ArrowLeftIcon className="h-4 w-4 mr-2" />
            Back
          </button>
        </div>

        {/* Product Details */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 lg:gap-12">
          {/* Product Images */}
          <div>
            <ProductImageGallery
              images={product?.images || []}
              productName={product?.name || ''}
            />
          </div>

          {/* Product Information */}
          <div className="space-y-6">
            {/* Header */}
            <div>
              <div className="flex items-start justify-between mb-2">
                <h1 className="text-3xl lg:text-4xl font-bold font-serif text-gray-900">
                  {product?.name}
                </h1>
                <button
                  onClick={handleShare}
                  className="p-2 text-gray-400 hover:text-gray-600 transition-colors"
                  aria-label="Share product"
                >
                  <ShareIcon className="h-5 w-5" />
                </button>
              </div>

              <div className="flex items-center space-x-2 mb-4">
                <span className="text-sm text-gray-500">SKU: {product?.sku}</span>
                {product?.is_featured && (
                  <span className="badge-gold flex items-center space-x-1">
                    <SparklesIcon className="h-3 w-3" />
                    <span>Featured</span>
                  </span>
                )}
              </div>

              {/* Price */}
              <div className="flex items-center space-x-4 mb-4">
                <span className="text-3xl font-bold text-primary-600">
                  {formatPrice(product?.price || 0)}
                </span>
                {product?.cost_price && product.cost_price < product.price && (
                  <span className="text-lg text-gray-500 line-through">
                    {formatPrice(product.cost_price)}
                  </span>
                )}
              </div>

              {/* Availability */}
              {availabilityInfo && (
                <div className={`inline-flex items-center space-x-2 px-3 py-2 rounded-full ${availabilityInfo.bgColor}`}>
                  {availabilityInfo.icon}
                  <span className={`text-sm font-medium ${availabilityInfo.color}`}>
                    {availabilityInfo.text}
                  </span>
                  {product?.stock_quantity !== undefined && product.stock_quantity > 0 && (
                    <span className="text-sm text-gray-600">
                      ({product.stock_quantity} available)
                    </span>
                  )}
                </div>
              )}
            </div>

            {/* Quick Description */}
            {product?.description && (
              <div className="prose prose-gray max-w-none">
                <p className="text-gray-600 leading-relaxed">
                  {product.description}
                </p>
              </div>
            )}

            {/* Key Specifications */}
            <div className="grid grid-cols-2 gap-4 p-4 bg-gray-50 rounded-lg">
              {product?.material && (
                <div>
                  <span className="text-sm text-gray-500">Material</span>
                  <p className="font-medium text-gray-900">{product.material}</p>
                </div>
              )}
              {product?.gemstone && (
                <div>
                  <span className="text-sm text-gray-500">Gemstone</span>
                  <p className="font-medium text-gray-900">{product.gemstone}</p>
                </div>
              )}
              {product?.metal_purity && (
                <div>
                  <span className="text-sm text-gray-500">Purity</span>
                  <p className="font-medium text-gray-900">{product.metal_purity}</p>
                </div>
              )}
              {product?.weight && (
                <div>
                  <span className="text-sm text-gray-500">Weight</span>
                  <p className="font-medium text-gray-900">{product.weight}g</p>
                </div>
              )}
            </div>

            {/* Quantity and Actions */}
            <div className="space-y-4">
              {/* Quantity Selector */}
              {product?.availability === 'available' && (
                <div className="flex items-center space-x-4">
                  <label className="text-sm font-medium text-gray-700">Quantity:</label>
                  <div className="flex items-center border border-gray-300 rounded-md">
                    <button
                      onClick={() => setQuantity(Math.max(1, quantity - 1))}
                      className="px-3 py-2 text-gray-600 hover:text-gray-800 hover:bg-gray-50"
                      disabled={quantity <= 1}
                    >
                      -
                    </button>
                    <input
                      type="number"
                      min="1"
                      max={product?.stock_quantity || 1}
                      value={quantity}
                      onChange={(e) => setQuantity(Math.max(1, parseInt(e.target.value) || 1))}
                      className="w-16 px-3 py-2 text-center border-0 focus:ring-0"
                    />
                    <button
                      onClick={() => setQuantity(Math.min(product?.stock_quantity || 1, quantity + 1))}
                      className="px-3 py-2 text-gray-600 hover:text-gray-800 hover:bg-gray-50"
                      disabled={quantity >= (product?.stock_quantity || 1)}
                    >
                      +
                    </button>
                  </div>
                  {currentQuantityInCart > 0 && (
                    <span className="text-sm text-gray-500">
                      ({currentQuantityInCart} in cart)
                    </span>
                  )}
                </div>
              )}

              {/* Action Buttons */}
              <div className="flex flex-col sm:flex-row gap-3">
                <button
                  onClick={handleAddToCart}
                  disabled={product?.availability !== 'available'}
                  className="btn-primary btn-lg flex-1 flex items-center justify-center space-x-2 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <ShoppingBagIcon className="h-5 w-5" />
                  <span>
                    {product?.availability === 'available'
                      ? (currentQuantityInCart > 0 ? 'Add More to Cart' : 'Add to Cart')
                      : 'Out of Stock'
                    }
                  </span>
                </button>

                <button
                  onClick={handleToggleWishlist}
                  className="btn-outline btn-lg flex items-center justify-center space-x-2"
                >
                  {isInWishlist(product?.id || '') ? (
                    <HeartSolidIcon className="h-5 w-5 text-red-500" />
                  ) : (
                    <HeartIcon className="h-5 w-5" />
                  )}
                  <span>
                    {isInWishlist(product?.id || '') ? 'Remove from Wishlist' : 'Add to Wishlist'}
                  </span>
                </button>
              </div>
            </div>

            {/* Category and Tags */}
            <div className="space-y-3">
              <div>
                <span className="text-sm text-gray-500">Category: </span>
                <span className="badge bg-gray-100 text-gray-800">
                  {product?.category.replace('_', ' ')}
                </span>
                {product?.subcategory && (
                  <span className="badge bg-gray-100 text-gray-800 ml-2">
                    {product.subcategory.replace('_', ' ')}
                  </span>
                )}
              </div>

              {product?.tags && product.tags.length > 0 && (
                <div>
                  <span className="text-sm text-gray-500">Tags: </span>
                  <div className="flex flex-wrap gap-2 mt-1">
                    {product.tags.map((tag, index) => (
                      <span key={index} className="badge bg-primary-100 text-primary-800">
                        {tag}
                      </span>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Product Details Tabs */}
        <div className="mt-16">
          <div className="border-b border-gray-200">
            <nav className="-mb-px flex space-x-8">
              {['description', 'specifications', 'care'].map((tab) => (
                <button
                  key={tab}
                  onClick={() => setSelectedTab(tab as any)}
                  className={`py-4 px-1 border-b-2 font-medium text-sm capitalize transition-colors ${
                    selectedTab === tab
                      ? 'border-primary-500 text-primary-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  {tab === 'care' ? 'Care Instructions' : tab}
                </button>
              ))}
            </nav>
          </div>

          <div className="py-8">
            {selectedTab === 'description' && (
              <div className="prose prose-gray max-w-none">
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900 mb-4">Product Description</h3>
                    <p className="text-gray-600 leading-relaxed mb-6">
                      {product?.description || 'No detailed description available for this product.'}
                    </p>

                    {product?.dimensions && (
                      <div>
                        <h4 className="font-medium text-gray-900 mb-2">Dimensions</h4>
                        <div className="flex items-center space-x-2 text-gray-600">
                          <ScaleIcon className="h-4 w-4" />
                          <span>
                            {product.dimensions.length} × {product.dimensions.width} × {product.dimensions.height} {product.dimensions.unit}
                          </span>
                        </div>
                      </div>
                    )}
                  </div>

                  <div className="bg-gray-50 p-6 rounded-lg">
                    <h4 className="font-medium text-gray-900 mb-4">Why Choose This Piece?</h4>
                    <ul className="space-y-2 text-gray-600">
                      <li className="flex items-start space-x-2">
                        <CheckCircleIcon className="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
                        <span>Handcrafted with premium materials</span>
                      </li>
                      <li className="flex items-start space-x-2">
                        <CheckCircleIcon className="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
                        <span>Quality assured and certified</span>
                      </li>
                      <li className="flex items-start space-x-2">
                        <CheckCircleIcon className="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
                        <span>Perfect for special occasions</span>
                      </li>
                      <li className="flex items-start space-x-2">
                        <CheckCircleIcon className="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
                        <span>Comes with authenticity certificate</span>
                      </li>
                    </ul>
                  </div>
                </div>
              </div>
            )}

            {selectedTab === 'specifications' && (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold text-gray-900">Technical Specifications</h3>
                  <dl className="space-y-3">
                    <div className="flex justify-between py-2 border-b border-gray-100">
                      <dt className="text-gray-600">SKU</dt>
                      <dd className="font-medium text-gray-900">{product?.sku}</dd>
                    </div>
                    {product?.material && (
                      <div className="flex justify-between py-2 border-b border-gray-100">
                        <dt className="text-gray-600">Material</dt>
                        <dd className="font-medium text-gray-900">{product.material}</dd>
                      </div>
                    )}
                    {product?.gemstone && (
                      <div className="flex justify-between py-2 border-b border-gray-100">
                        <dt className="text-gray-600">Gemstone</dt>
                        <dd className="font-medium text-gray-900">{product.gemstone}</dd>
                      </div>
                    )}
                    {product?.metal_purity && (
                      <div className="flex justify-between py-2 border-b border-gray-100">
                        <dt className="text-gray-600">Metal Purity</dt>
                        <dd className="font-medium text-gray-900">{product.metal_purity}</dd>
                      </div>
                    )}
                    {product?.weight && (
                      <div className="flex justify-between py-2 border-b border-gray-100">
                        <dt className="text-gray-600">Weight</dt>
                        <dd className="font-medium text-gray-900">{product.weight} grams</dd>
                      </div>
                    )}
                    <div className="flex justify-between py-2 border-b border-gray-100">
                      <dt className="text-gray-600">Category</dt>
                      <dd className="font-medium text-gray-900">{product?.category.replace('_', ' ')}</dd>
                    </div>
                    {product?.subcategory && (
                      <div className="flex justify-between py-2 border-b border-gray-100">
                        <dt className="text-gray-600">Subcategory</dt>
                        <dd className="font-medium text-gray-900">{product.subcategory.replace('_', ' ')}</dd>
                      </div>
                    )}
                  </dl>
                </div>

                {product?.dimensions && (
                  <div className="space-y-4">
                    <h3 className="text-lg font-semibold text-gray-900">Dimensions</h3>
                    <div className="bg-gray-50 p-4 rounded-lg">
                      <div className="grid grid-cols-3 gap-4 text-center">
                        <div>
                          <div className="text-2xl font-bold text-primary-600">{product.dimensions.length}</div>
                          <div className="text-sm text-gray-500">Length ({product.dimensions.unit})</div>
                        </div>
                        <div>
                          <div className="text-2xl font-bold text-primary-600">{product.dimensions.width}</div>
                          <div className="text-sm text-gray-500">Width ({product.dimensions.unit})</div>
                        </div>
                        <div>
                          <div className="text-2xl font-bold text-primary-600">{product.dimensions.height}</div>
                          <div className="text-sm text-gray-500">Height ({product.dimensions.unit})</div>
                        </div>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            )}

            {selectedTab === 'care' && (
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">Care Instructions</h3>
                  <div className="space-y-4">
                    <div className="p-4 bg-blue-50 rounded-lg">
                      <h4 className="font-medium text-blue-900 mb-2">Daily Care</h4>
                      <ul className="text-blue-800 text-sm space-y-1">
                        <li>• Store in a clean, dry place</li>
                        <li>• Avoid contact with perfumes and chemicals</li>
                        <li>• Remove before swimming or exercising</li>
                        <li>• Clean gently with a soft cloth</li>
                      </ul>
                    </div>

                    <div className="p-4 bg-green-50 rounded-lg">
                      <h4 className="font-medium text-green-900 mb-2">Cleaning</h4>
                      <ul className="text-green-800 text-sm space-y-1">
                        <li>• Use mild soap and warm water</li>
                        <li>• Dry thoroughly with a soft cloth</li>
                        <li>• For deep cleaning, consult a professional</li>
                        <li>• Avoid harsh chemicals and abrasives</li>
                      </ul>
                    </div>
                  </div>
                </div>

                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">Storage Tips</h3>
                  <div className="space-y-4">
                    <div className="p-4 bg-yellow-50 rounded-lg">
                      <h4 className="font-medium text-yellow-900 mb-2">Proper Storage</h4>
                      <ul className="text-yellow-800 text-sm space-y-1">
                        <li>• Keep in individual pouches or compartments</li>
                        <li>• Avoid exposure to direct sunlight</li>
                        <li>• Store in a cool, dry environment</li>
                        <li>• Use anti-tarnish strips for silver jewelry</li>
                      </ul>
                    </div>

                    <div className="p-4 bg-purple-50 rounded-lg">
                      <h4 className="font-medium text-purple-900 mb-2">Professional Service</h4>
                      <p className="text-purple-800 text-sm">
                        We recommend professional cleaning and inspection every 6-12 months
                        to maintain the beauty and integrity of your jewelry.
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProductPage;
