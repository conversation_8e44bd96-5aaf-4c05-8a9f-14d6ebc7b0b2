// Product Types
export interface ProductImage {
  id: string;
  product_id: string;
  image_url: string;
  thumbnail_url?: string;
  medium_url?: string;
  large_url?: string;
  alt_text?: string;
  display_order: number;
  is_primary: boolean;
  file_size?: number;
  width?: number;
  height?: number;
  created_at: string;
}

export interface Product {
  id: string;
  sku: string;
  name: string;
  description?: string;
  price: number;
  cost_price?: number;
  category: string;
  subcategory?: string;
  weight?: number;
  material?: string;
  gemstone?: string;
  metal_purity?: string;
  dimensions?: {
    length: number;
    width: number;
    height: number;
    unit: string;
  };
  availability: 'available' | 'out_of_stock' | 'discontinued';
  stock_quantity: number;
  min_stock_level: number;
  tags: string[];
  is_featured: boolean;
  is_active: boolean;
  images?: ProductImage[];
  primary_image?: ProductImage;
  created_at: string;
  updated_at: string;
}

// Collection Types
export interface Collection {
  id: string;
  slug: string;
  name: string;
  description?: string;
  cover_image_url?: string;
  is_active: boolean;
  is_public: boolean;
  expires_at?: string;
  view_count: number;
  share_count: number;
  created_at: string;
  updated_at: string;
  products?: Product[];
  product_count?: number;
}

// Customer Types
export interface Customer {
  id: string;
  name: string;
  email?: string;
  phone: string;
  address?: {
    street: string;
    city: string;
    state: string;
    country: string;
    postal_code: string;
  };
  notes?: string;
  total_orders: number;
  total_spent: number;
  first_order_at?: string;
  last_order_at?: string;
  created_at: string;
  updated_at: string;
}

// Order Types
export interface OrderItem {
  id: string;
  order_id: string;
  product_id: string;
  product_name: string;
  product_sku: string;
  unit_price: number;
  quantity: number;
  total_price: number;
  product_weight?: number;
  product_material?: string;
  product_dimensions?: any;
  created_at: string;
}

export interface Order {
  id: string;
  order_number: string;
  collection_id?: string;
  customer_id?: string;
  customer_name: string;
  customer_phone: string;
  customer_email?: string;
  customer_address?: any;
  subtotal: number;
  tax_amount: number;
  discount_amount: number;
  total_amount: number;
  currency: string;
  special_instructions?: string;
  admin_notes?: string;
  payment_method?: string;
  payment_status: string;
  status: 'pending' | 'confirmed' | 'processing' | 'shipped' | 'delivered' | 'cancelled';
  created_at: string;
  updated_at: string;
  confirmed_at?: string;
  shipped_at?: string;
  delivered_at?: string;
  cancelled_at?: string;
  items: OrderItem[];
}

// Cart Types
export interface CartItem {
  product: Product;
  quantity: number;
  selected_image?: ProductImage;
}

export interface Cart {
  items: CartItem[];
  total_items: number;
  subtotal: number;
  tax_amount: number;
  total_amount: number;
}

// Wishlist Types
export interface WishlistItem {
  product: Product;
  added_at: string;
}

// API Response Types
export interface PaginatedResponse<T> {
  data: T[];
  total: number;
  page: number;
  limit: number;
  total_pages: number;
}

export interface ApiResponse<T> {
  data: T;
  message?: string;
  success: boolean;
}

// Filter and Search Types
export interface FilterOptions {
  search?: string;
  category?: string;
  subcategory?: string;
  material?: string;
  gemstone?: string;
  price_min?: number;
  price_max?: number;
  availability?: string;
  is_featured?: boolean;
  is_public?: boolean;
  tags?: string[];
  sort_by?: 'name' | 'price' | 'created_at' | 'popularity';
  sort_order?: 'asc' | 'desc';
  page?: number;
  limit?: number;
}

// Form Types
export interface CustomerFormData {
  name: string;
  email?: string;
  phone: string;
  address?: {
    street: string;
    city: string;
    state: string;
    country?: string;
    postal_code: string;
  };
  special_instructions?: string;
}

export interface OrderFormData extends CustomerFormData {
  items: CartItem[];
  collection_id?: string;
}

// UI State Types
export interface LoadingState {
  isLoading: boolean;
  error?: string;
}

export interface NotificationState {
  type: 'success' | 'error' | 'warning' | 'info';
  message: string;
  id: string;
}
