apiVersion: apps/v1
kind: Deployment
metadata:
  name: backend
  namespace: jewelry-store
  labels:
    app: backend
spec:
  replicas: 2
  selector:
    matchLabels:
      app: backend
  template:
    metadata:
      labels:
        app: backend
    spec:
      containers:
      - name: backend
        image: asia-southeast1-docker.pkg.dev/emacs-464306/prod/anandjewels/backend:dfe40fa687d0
        ports:
        - containerPort: 8080
        env:
        - name: PORT
          valueFrom:
            configMapKeyRef:
              name: backend-config
              key: PORT
        - name: GIN_MODE
          valueFrom:
            configMapKeyRef:
              name: backend-config
              key: GIN_MODE
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: backend-secrets
              key: DATABASE_URL
        - name: REDIS_URL
          valueFrom:
            secretKeyRef:
              name: backend-secrets
              key: REDIS_URL
        - name: MINIO_ENDPOINT
          valueFrom:
            secretKeyRef:
              name: backend-secrets
              key: MINIO_ENDPOINT
        - name: MINIO_ACCESS_KEY
          valueFrom:
            secretKeyRef:
              name: backend-secrets
              key: MINIO_ACCESS_KEY
        - name: MINIO_SECRET_KEY
          valueFrom:
            secretKeyRef:
              name: backend-secrets
              key: MINIO_SECRET_KEY
        - name: MINIO_BUCKET_NAME
          valueFrom:
            configMapKeyRef:
              name: backend-config
              key: MINIO_BUCKET_NAME
        - name: MINIO_USE_SSL
          valueFrom:
            configMapKeyRef:
              name: backend-config
              key: MINIO_USE_SSL
        - name: JWT_SECRET
          valueFrom:
            secretKeyRef:
              name: backend-secrets
              key: JWT_SECRET
        resources:
          requests:
            memory: "256Mi"
            cpu: "250m"
          limits:
            memory: "512Mi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 5
          periodSeconds: 5
---
apiVersion: v1
kind: Service
metadata:
  name: backend-service
  namespace: jewelry-store
  labels:
    app: backend
spec:
  selector:
    app: backend
  ports:
  - port: 8080
    targetPort: 8080
  type: ClusterIP
