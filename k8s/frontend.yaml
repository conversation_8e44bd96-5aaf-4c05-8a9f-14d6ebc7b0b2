apiVersion: apps/v1
kind: Deployment
metadata:
  name: frontend
  namespace: jewelry-store
  labels:
    app: frontend
spec:
  replicas: 2
  selector:
    matchLabels:
      app: frontend
  template:
    metadata:
      labels:
        app: frontend
    spec:
      containers:
      - name: frontend
        image: asia-southeast1-docker.pkg.dev/emacs-464306/prod/anandjewels/frontend:1
        ports:
        - containerPort: 8080
        env:
        - name: VITE_API_URL
          value: "http://backend-service:8080/api/v1"
        - name: VITE_APP_NAME
          valueFrom:
            configMapKeyRef:
              name: frontend-config
              key: VITE_APP_NAME
        - name: VITE_DEBUG_MODE
          valueFrom:
            configMapKeyRef:
              name: frontend-config
              key: VITE_DEBUG_MODE
        resources:
          requests:
            memory: "128Mi"
            cpu: "100m"
          limits:
            memory: "256Mi"
            cpu: "200m"
        livenessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 5
          periodSeconds: 5
---
apiVersion: v1
kind: Service
metadata:
  name: frontend-service
  namespace: jewelry-store
  labels:
    app: frontend
spec:
  selector:
    app: frontend
  ports:
  - port: 80
    targetPort: 8080
  type: ClusterIP
